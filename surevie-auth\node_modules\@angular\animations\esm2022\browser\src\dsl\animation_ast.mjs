const EMPTY_ANIMATION_OPTIONS = {};
export {};
//# sourceMappingURL=data:application/json;base64,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