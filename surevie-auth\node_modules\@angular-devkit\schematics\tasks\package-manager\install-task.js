"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.NodePackageInstallTask = void 0;
const options_1 = require("./options");
class NodePackageInstallTask {
    quiet = true;
    hideOutput = true;
    allowScripts = false;
    workingDirectory;
    packageManager;
    packageName;
    constructor(options) {
        if (typeof options === 'string') {
            this.workingDirectory = options;
        }
        else if (typeof options === 'object') {
            if (options.quiet != undefined) {
                this.quiet = options.quiet;
            }
            if (options.hideOutput != undefined) {
                this.hideOutput = options.hideOutput;
            }
            if (options.workingDirectory != undefined) {
                this.workingDirectory = options.workingDirectory;
            }
            if (options.packageManager != undefined) {
                this.packageManager = options.packageManager;
            }
            if (options.packageName != undefined) {
                this.packageName = options.packageName;
            }
            if (options.allowScripts !== undefined) {
                this.allowScripts = options.allowScripts;
            }
        }
    }
    toConfiguration() {
        return {
            name: options_1.NodePackageName,
            options: {
                command: 'install',
                quiet: this.quiet,
                hideOutput: this.hideOutput,
                workingDirectory: this.workingDirectory,
                packageManager: this.packageManager,
                packageName: this.packageName,
                allowScripts: this.allowScripts,
            },
        };
    }
}
exports.NodePackageInstallTask = NodePackageInstallTask;
