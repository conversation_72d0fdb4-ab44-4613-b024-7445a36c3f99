/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { Location } from '@angular/common';
import { EnvironmentInjector, inject, Injectable } from '@angular/core';
import { BehaviorSubject, combineLatest, EMPTY, from, of, Subject } from 'rxjs';
import { catchError, defaultIfEmpty, filter, finalize, map, switchMap, take, takeUntil, tap, } from 'rxjs/operators';
import { createRouterState } from './create_router_state';
import { INPUT_BINDER } from './directives/router_outlet';
import { BeforeActivateRoutes, GuardsCheckEnd, GuardsCheckStart, IMPERATIVE_NAVIGATION, NavigationCancel, NavigationCancellationCode, NavigationEnd, NavigationError, NavigationSkipped, NavigationSkippedCode, NavigationStart, RedirectRequest, ResolveEnd, ResolveStart, RouteConfigLoadEnd, RouteConfigLoadStart, RoutesRecognized, } from './events';
import { isNavigationCancelingError, isRedirectingNavigationCancelingError, redirectingNavigationError, } from './navigation_canceling_error';
import { activateRoutes } from './operators/activate_routes';
import { checkGuards } from './operators/check_guards';
import { recognize } from './operators/recognize';
import { resolveData } from './operators/resolve_data';
import { switchTap } from './operators/switch_tap';
import { TitleStrategy } from './page_title_strategy';
import { ROUTER_CONFIGURATION } from './router_config';
import { RouterConfigLoader } from './router_config_loader';
import { ChildrenOutletContexts } from './router_outlet_context';
import { createEmptyState, } from './router_state';
import { UrlHandlingStrategy } from './url_handling_strategy';
import { isUrlTree, UrlSerializer } from './url_tree';
import { getAllRouteGuards } from './utils/preactivation';
import { CREATE_VIEW_TRANSITION } from './utils/view_transition';
import * as i0 from "@angular/core";
export class NavigationTransitions {
    get hasRequestedNavigation() {
        return this.navigationId !== 0;
    }
    constructor() {
        this.currentNavigation = null;
        this.currentTransition = null;
        this.lastSuccessfulNavigation = null;
        /**
         * These events are used to communicate back to the Router about the state of the transition. The
         * Router wants to respond to these events in various ways. Because the `NavigationTransition`
         * class is not public, this event subject is not publicly exposed.
         */
        this.events = new Subject();
        /**
         * Used to abort the current transition with an error.
         */
        this.transitionAbortSubject = new Subject();
        this.configLoader = inject(RouterConfigLoader);
        this.environmentInjector = inject(EnvironmentInjector);
        this.urlSerializer = inject(UrlSerializer);
        this.rootContexts = inject(ChildrenOutletContexts);
        this.location = inject(Location);
        this.inputBindingEnabled = inject(INPUT_BINDER, { optional: true }) !== null;
        this.titleStrategy = inject(TitleStrategy);
        this.options = inject(ROUTER_CONFIGURATION, { optional: true }) || {};
        this.paramsInheritanceStrategy = this.options.paramsInheritanceStrategy || 'emptyOnly';
        this.urlHandlingStrategy = inject(UrlHandlingStrategy);
        this.createViewTransition = inject(CREATE_VIEW_TRANSITION, { optional: true });
        this.navigationId = 0;
        /**
         * Hook that enables you to pause navigation after the preactivation phase.
         * Used by `RouterModule`.
         *
         * @internal
         */
        this.afterPreactivation = () => of(void 0);
        /** @internal */
        this.rootComponentType = null;
        const onLoadStart = (r) => this.events.next(new RouteConfigLoadStart(r));
        const onLoadEnd = (r) => this.events.next(new RouteConfigLoadEnd(r));
        this.configLoader.onLoadEndListener = onLoadEnd;
        this.configLoader.onLoadStartListener = onLoadStart;
    }
    complete() {
        this.transitions?.complete();
    }
    handleNavigationRequest(request) {
        const id = ++this.navigationId;
        this.transitions?.next({ ...this.transitions.value, ...request, id });
    }
    setupNavigations(router, initialUrlTree, initialRouterState) {
        this.transitions = new BehaviorSubject({
            id: 0,
            currentUrlTree: initialUrlTree,
            currentRawUrl: initialUrlTree,
            extractedUrl: this.urlHandlingStrategy.extract(initialUrlTree),
            urlAfterRedirects: this.urlHandlingStrategy.extract(initialUrlTree),
            rawUrl: initialUrlTree,
            extras: {},
            resolve: null,
            reject: null,
            promise: Promise.resolve(true),
            source: IMPERATIVE_NAVIGATION,
            restoredState: null,
            currentSnapshot: initialRouterState.snapshot,
            targetSnapshot: null,
            currentRouterState: initialRouterState,
            targetRouterState: null,
            guards: { canActivateChecks: [], canDeactivateChecks: [] },
            guardsResult: null,
        });
        return this.transitions.pipe(filter((t) => t.id !== 0), 
        // Extract URL
        map((t) => ({
            ...t,
            extractedUrl: this.urlHandlingStrategy.extract(t.rawUrl),
        })), 
        // Using switchMap so we cancel executing navigations when a new one comes in
        switchMap((overallTransitionState) => {
            let completed = false;
            let errored = false;
            return of(overallTransitionState).pipe(switchMap((t) => {
                // It is possible that `switchMap` fails to cancel previous navigations if a new one happens synchronously while the operator
                // is processing the `next` notification of that previous navigation. This can happen when a new navigation (say 2) cancels a
                // previous one (1) and yet another navigation (3) happens synchronously in response to the `NavigationCancel` event for (1).
                // https://github.com/ReactiveX/rxjs/issues/7455
                if (this.navigationId > overallTransitionState.id) {
                    const cancellationReason = typeof ngDevMode === 'undefined' || ngDevMode
                        ? `Navigation ID ${overallTransitionState.id} is not equal to the current navigation id ${this.navigationId}`
                        : '';
                    this.cancelNavigationTransition(overallTransitionState, cancellationReason, NavigationCancellationCode.SupersededByNewNavigation);
                    return EMPTY;
                }
                this.currentTransition = overallTransitionState;
                // Store the Navigation object
                this.currentNavigation = {
                    id: t.id,
                    initialUrl: t.rawUrl,
                    extractedUrl: t.extractedUrl,
                    trigger: t.source,
                    extras: t.extras,
                    previousNavigation: !this.lastSuccessfulNavigation
                        ? null
                        : {
                            ...this.lastSuccessfulNavigation,
                            previousNavigation: null,
                        },
                };
                const urlTransition = !router.navigated || this.isUpdatingInternalState() || this.isUpdatedBrowserUrl();
                const onSameUrlNavigation = t.extras.onSameUrlNavigation ?? router.onSameUrlNavigation;
                if (!urlTransition && onSameUrlNavigation !== 'reload') {
                    const reason = typeof ngDevMode === 'undefined' || ngDevMode
                        ? `Navigation to ${t.rawUrl} was ignored because it is the same as the current Router URL.`
                        : '';
                    this.events.next(new NavigationSkipped(t.id, this.urlSerializer.serialize(t.rawUrl), reason, NavigationSkippedCode.IgnoredSameUrlNavigation));
                    t.resolve(null);
                    return EMPTY;
                }
                if (this.urlHandlingStrategy.shouldProcessUrl(t.rawUrl)) {
                    return of(t).pipe(
                    // Fire NavigationStart event
                    switchMap((t) => {
                        const transition = this.transitions?.getValue();
                        this.events.next(new NavigationStart(t.id, this.urlSerializer.serialize(t.extractedUrl), t.source, t.restoredState));
                        if (transition !== this.transitions?.getValue()) {
                            return EMPTY;
                        }
                        // This delay is required to match old behavior that forced
                        // navigation to always be async
                        return Promise.resolve(t);
                    }), 
                    // Recognize
                    recognize(this.environmentInjector, this.configLoader, this.rootComponentType, router.config, this.urlSerializer, this.paramsInheritanceStrategy), 
                    // Update URL if in `eager` update mode
                    tap((t) => {
                        overallTransitionState.targetSnapshot = t.targetSnapshot;
                        overallTransitionState.urlAfterRedirects = t.urlAfterRedirects;
                        this.currentNavigation = {
                            ...this.currentNavigation,
                            finalUrl: t.urlAfterRedirects,
                        };
                        // Fire RoutesRecognized
                        const routesRecognized = new RoutesRecognized(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);
                        this.events.next(routesRecognized);
                    }));
                }
                else if (urlTransition &&
                    this.urlHandlingStrategy.shouldProcessUrl(t.currentRawUrl)) {
                    /* When the current URL shouldn't be processed, but the previous one
                     * was, we handle this "error condition" by navigating to the
                     * previously successful URL, but leaving the URL intact.*/
                    const { id, extractedUrl, source, restoredState, extras } = t;
                    const navStart = new NavigationStart(id, this.urlSerializer.serialize(extractedUrl), source, restoredState);
                    this.events.next(navStart);
                    const targetSnapshot = createEmptyState(this.rootComponentType).snapshot;
                    this.currentTransition = overallTransitionState = {
                        ...t,
                        targetSnapshot,
                        urlAfterRedirects: extractedUrl,
                        extras: { ...extras, skipLocationChange: false, replaceUrl: false },
                    };
                    this.currentNavigation.finalUrl = extractedUrl;
                    return of(overallTransitionState);
                }
                else {
                    /* When neither the current or previous URL can be processed, do
                     * nothing other than update router's internal reference to the
                     * current "settled" URL. This way the next navigation will be coming
                     * from the current URL in the browser.
                     */
                    const reason = typeof ngDevMode === 'undefined' || ngDevMode
                        ? `Navigation was ignored because the UrlHandlingStrategy` +
                            ` indicated neither the current URL ${t.currentRawUrl} nor target URL ${t.rawUrl} should be processed.`
                        : '';
                    this.events.next(new NavigationSkipped(t.id, this.urlSerializer.serialize(t.extractedUrl), reason, NavigationSkippedCode.IgnoredByUrlHandlingStrategy));
                    t.resolve(null);
                    return EMPTY;
                }
            }), 
            // --- GUARDS ---
            tap((t) => {
                const guardsStart = new GuardsCheckStart(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);
                this.events.next(guardsStart);
            }), map((t) => {
                this.currentTransition = overallTransitionState = {
                    ...t,
                    guards: getAllRouteGuards(t.targetSnapshot, t.currentSnapshot, this.rootContexts),
                };
                return overallTransitionState;
            }), checkGuards(this.environmentInjector, (evt) => this.events.next(evt)), tap((t) => {
                overallTransitionState.guardsResult = t.guardsResult;
                if (isUrlTree(t.guardsResult)) {
                    throw redirectingNavigationError(this.urlSerializer, t.guardsResult);
                }
                const guardsEnd = new GuardsCheckEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot, !!t.guardsResult);
                this.events.next(guardsEnd);
            }), filter((t) => {
                if (!t.guardsResult) {
                    this.cancelNavigationTransition(t, '', NavigationCancellationCode.GuardRejected);
                    return false;
                }
                return true;
            }), 
            // --- RESOLVE ---
            switchTap((t) => {
                if (t.guards.canActivateChecks.length) {
                    return of(t).pipe(tap((t) => {
                        const resolveStart = new ResolveStart(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);
                        this.events.next(resolveStart);
                    }), switchMap((t) => {
                        let dataResolved = false;
                        return of(t).pipe(resolveData(this.paramsInheritanceStrategy, this.environmentInjector), tap({
                            next: () => (dataResolved = true),
                            complete: () => {
                                if (!dataResolved) {
                                    this.cancelNavigationTransition(t, typeof ngDevMode === 'undefined' || ngDevMode
                                        ? `At least one route resolver didn't emit any value.`
                                        : '', NavigationCancellationCode.NoDataFromResolver);
                                }
                            },
                        }));
                    }), tap((t) => {
                        const resolveEnd = new ResolveEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects), t.targetSnapshot);
                        this.events.next(resolveEnd);
                    }));
                }
                return undefined;
            }), 
            // --- LOAD COMPONENTS ---
            switchTap((t) => {
                const loadComponents = (route) => {
                    const loaders = [];
                    if (route.routeConfig?.loadComponent && !route.routeConfig._loadedComponent) {
                        loaders.push(this.configLoader.loadComponent(route.routeConfig).pipe(tap((loadedComponent) => {
                            route.component = loadedComponent;
                        }), map(() => void 0)));
                    }
                    for (const child of route.children) {
                        loaders.push(...loadComponents(child));
                    }
                    return loaders;
                };
                return combineLatest(loadComponents(t.targetSnapshot.root)).pipe(defaultIfEmpty(null), take(1));
            }), switchTap(() => this.afterPreactivation()), switchMap(() => {
                const { currentSnapshot, targetSnapshot } = overallTransitionState;
                const viewTransitionStarted = this.createViewTransition?.(this.environmentInjector, currentSnapshot.root, targetSnapshot.root);
                // If view transitions are enabled, block the navigation until the view
                // transition callback starts. Otherwise, continue immediately.
                return viewTransitionStarted
                    ? from(viewTransitionStarted).pipe(map(() => overallTransitionState))
                    : of(overallTransitionState);
            }), map((t) => {
                const targetRouterState = createRouterState(router.routeReuseStrategy, t.targetSnapshot, t.currentRouterState);
                this.currentTransition = overallTransitionState = { ...t, targetRouterState };
                this.currentNavigation.targetRouterState = targetRouterState;
                return overallTransitionState;
            }), tap(() => {
                this.events.next(new BeforeActivateRoutes());
            }), activateRoutes(this.rootContexts, router.routeReuseStrategy, (evt) => this.events.next(evt), this.inputBindingEnabled), 
            // Ensure that if some observable used to drive the transition doesn't
            // complete, the navigation still finalizes This should never happen, but
            // this is done as a safety measure to avoid surfacing this error (#49567).
            take(1), tap({
                next: (t) => {
                    completed = true;
                    this.lastSuccessfulNavigation = this.currentNavigation;
                    this.events.next(new NavigationEnd(t.id, this.urlSerializer.serialize(t.extractedUrl), this.urlSerializer.serialize(t.urlAfterRedirects)));
                    this.titleStrategy?.updateTitle(t.targetRouterState.snapshot);
                    t.resolve(true);
                },
                complete: () => {
                    completed = true;
                },
            }), 
            // There used to be a lot more logic happening directly within the
            // transition Observable. Some of this logic has been refactored out to
            // other places but there may still be errors that happen there. This gives
            // us a way to cancel the transition from the outside. This may also be
            // required in the future to support something like the abort signal of the
            // Navigation API where the navigation gets aborted from outside the
            // transition.
            takeUntil(this.transitionAbortSubject.pipe(tap((err) => {
                throw err;
            }))), finalize(() => {
                /* When the navigation stream finishes either through error or success,
                 * we set the `completed` or `errored` flag. However, there are some
                 * situations where we could get here without either of those being set.
                 * For instance, a redirect during NavigationStart. Therefore, this is a
                 * catch-all to make sure the NavigationCancel event is fired when a
                 * navigation gets cancelled but not caught by other means. */
                if (!completed && !errored) {
                    const cancelationReason = typeof ngDevMode === 'undefined' || ngDevMode
                        ? `Navigation ID ${overallTransitionState.id} is not equal to the current navigation id ${this.navigationId}`
                        : '';
                    this.cancelNavigationTransition(overallTransitionState, cancelationReason, NavigationCancellationCode.SupersededByNewNavigation);
                }
                // Only clear current navigation if it is still set to the one that
                // finalized.
                if (this.currentTransition?.id === overallTransitionState.id) {
                    this.currentNavigation = null;
                    this.currentTransition = null;
                }
            }), catchError((e) => {
                errored = true;
                /* This error type is issued during Redirect, and is handled as a
                 * cancellation rather than an error. */
                if (isNavigationCancelingError(e)) {
                    this.events.next(new NavigationCancel(overallTransitionState.id, this.urlSerializer.serialize(overallTransitionState.extractedUrl), e.message, e.cancellationCode));
                    // When redirecting, we need to delay resolving the navigation
                    // promise and push it to the redirect navigation
                    if (!isRedirectingNavigationCancelingError(e)) {
                        overallTransitionState.resolve(false);
                    }
                    else {
                        this.events.next(new RedirectRequest(e.url));
                    }
                    /* All other errors should reset to the router's internal URL reference
                     * to the pre-error state. */
                }
                else {
                    this.events.next(new NavigationError(overallTransitionState.id, this.urlSerializer.serialize(overallTransitionState.extractedUrl), e, overallTransitionState.targetSnapshot ?? undefined));
                    try {
                        overallTransitionState.resolve(router.errorHandler(e));
                    }
                    catch (ee) {
                        // TODO(atscott): consider flipping the default behavior of
                        // resolveNavigationPromiseOnError to be `resolve(false)` when
                        // undefined. This is the most sane thing to do given that
                        // applications very rarely handle the promise rejection and, as a
                        // result, would get "unhandled promise rejection" console logs.
                        // The vast majority of applications would not be affected by this
                        // change so omitting a migration seems reasonable. Instead,
                        // applications that rely on rejection can specifically opt-in to the
                        // old behavior.
                        if (this.options.resolveNavigationPromiseOnError) {
                            overallTransitionState.resolve(false);
                        }
                        else {
                            overallTransitionState.reject(ee);
                        }
                    }
                }
                return EMPTY;
            }));
            // casting because `pipe` returns observable({}) when called with 8+ arguments
        }));
    }
    cancelNavigationTransition(t, reason, code) {
        const navCancel = new NavigationCancel(t.id, this.urlSerializer.serialize(t.extractedUrl), reason, code);
        this.events.next(navCancel);
        t.resolve(false);
    }
    /**
     * @returns Whether we're navigating to somewhere that is not what the Router is
     * currently set to.
     */
    isUpdatingInternalState() {
        // TODO(atscott): The serializer should likely be used instead of
        // `UrlTree.toString()`. Custom serializers are often written to handle
        // things better than the default one (objects, for example will be
        // [Object object] with the custom serializer and be "the same" when they
        // aren't).
        // (Same for isUpdatedBrowserUrl)
        return (this.currentTransition?.extractedUrl.toString() !==
            this.currentTransition?.currentUrlTree.toString());
    }
    /**
     * @returns Whether we're updating the browser URL to something new (navigation is going
     * to somewhere not displayed in the URL bar and we will update the URL
     * bar if navigation succeeds).
     */
    isUpdatedBrowserUrl() {
        // The extracted URL is the part of the URL that this application cares about. `extract` may
        // return only part of the browser URL and that part may have not changed even if some other
        // portion of the URL did.
        const extractedBrowserUrl = this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(true)));
        return (extractedBrowserUrl.toString() !== this.currentTransition?.extractedUrl.toString() &&
            !this.currentTransition?.extras.skipLocationChange);
    }
    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NavigationTransitions, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }
    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NavigationTransitions, providedIn: 'root' }); }
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "17.3.12", ngImport: i0, type: NavigationTransitions, decorators: [{
            type: Injectable,
            args: [{ providedIn: 'root' }]
        }], ctorParameters: () => [] });
export function isBrowserTriggeredNavigation(source) {
    return source !== IMPERATIVE_NAVIGATION;
}
//# sourceMappingURL=data:application/json;base64,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