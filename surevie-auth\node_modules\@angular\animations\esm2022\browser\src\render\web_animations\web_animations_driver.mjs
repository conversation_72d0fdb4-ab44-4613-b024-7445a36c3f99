import { allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, camelCaseToDashCase, computeStyle, normalizeKeyframes, } from '../../util';
import { containsElement, getParentElement, invokeQuery, validateStyleProperty, validateWebAnimatableStyleProperty, } from '../shared';
import { packageNonAnimatableStyles } from '../special_cased_styles';
import { WebAnimationsPlayer } from './web_animations_player';
export class WebAnimationsDriver {
    validateStyleProperty(prop) {
        // Perform actual validation in dev mode only, in prod mode this check is a noop.
        if (typeof ngDevMode === 'undefined' || ngDevMode) {
            return validateStyleProperty(prop);
        }
        return true;
    }
    validateAnimatableStyleProperty(prop) {
        // Perform actual validation in dev mode only, in prod mode this check is a noop.
        if (typeof ngDevMode === 'undefined' || ngDevMode) {
            const cssProp = camelCaseToDashCase(prop);
            return validateWebAnimatableStyleProperty(cssProp);
        }
        return true;
    }
    matchesElement(_element, _selector) {
        // This method is deprecated and no longer in use so we return false.
        return false;
    }
    containsElement(elm1, elm2) {
        return containsElement(elm1, elm2);
    }
    getParentElement(element) {
        return getParentElement(element);
    }
    query(element, selector, multi) {
        return invokeQuery(element, selector, multi);
    }
    computeStyle(element, prop, defaultValue) {
        return computeStyle(element, prop);
    }
    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {
        const fill = delay == 0 ? 'both' : 'forwards';
        const playerOptions = { duration, delay, fill };
        // we check for this to avoid having a null|undefined value be present
        // for the easing (which results in an error for certain browsers #9752)
        if (easing) {
            playerOptions['easing'] = easing;
        }
        const previousStyles = new Map();
        const previousWebAnimationPlayers = (previousPlayers.filter((player) => player instanceof WebAnimationsPlayer));
        if (allowPreviousPlayerStylesMerge(duration, delay)) {
            previousWebAnimationPlayers.forEach((player) => {
                player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));
            });
        }
        let _keyframes = normalizeKeyframes(keyframes).map((styles) => new Map(styles));
        _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);
        const specialStyles = packageNonAnimatableStyles(element, _keyframes);
        return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);
    }
}
//# sourceMappingURL=data:application/json;base64,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