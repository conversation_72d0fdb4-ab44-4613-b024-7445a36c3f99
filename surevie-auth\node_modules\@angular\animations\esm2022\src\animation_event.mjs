/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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