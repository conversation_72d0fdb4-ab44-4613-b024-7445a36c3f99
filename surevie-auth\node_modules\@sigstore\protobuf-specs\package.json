{"name": "@sigstore/protobuf-specs", "version": "0.3.3", "description": "code-signing for npm packages", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc"}, "repository": {"type": "git", "url": "git+https://github.com/sigstore/protobuf-specs.git"}, "files": ["dist"], "author": "<EMAIL>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/sigstore/protobuf-specs/issues"}, "homepage": "https://github.com/sigstore/protobuf-specs#readme", "devDependencies": {"@tsconfig/node18": "^18.2.4", "@types/node": "^18.14.0", "typescript": "^5.7.2"}, "engines": {"node": "^18.17.0 || >=20.5.0"}}