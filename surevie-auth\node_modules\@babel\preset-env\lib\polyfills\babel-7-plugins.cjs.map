{"version": 3, "names": ["Object", "defineProperties", "exports", "pluginCoreJS2", "get", "require", "default", "pluginRegenerator", "legacyBabelPolyfillPlugin", "removeRegeneratorEntryPlugin", "corejs2Polyfills"], "sources": ["../../src/polyfills/babel-7-plugins.cjs"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n\nif (!process.env.BABEL_8_BREAKING) {\n  Object.defineProperties(exports, {\n    pluginCoreJS2: {\n      get: () => require(\"babel-plugin-polyfill-corejs2\").default,\n    },\n    pluginRegenerator: {\n      get: () => require(\"babel-plugin-polyfill-regenerator\").default,\n    },\n    legacyBabelPolyfillPlugin: { get: () => require(\"./babel-polyfill.cjs\") },\n    removeRegeneratorEntryPlugin: { get: () => require(\"./regenerator.cjs\") },\n    corejs2Polyfills: {\n      get: () => require(\"@babel/compat-data/corejs2-built-ins\"),\n    },\n  });\n}\n"], "mappings": "AAEmC;EACjCA,MAAM,CAACC,gBAAgB,CAACC,OAAO,EAAE;IAC/BC,aAAa,EAAE;MACbC,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC,+BAA+B,CAAC,CAACC;IACtD,CAAC;IACDC,iBAAiB,EAAE;MACjBH,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC,mCAAmC,CAAC,CAACC;IAC1D,CAAC;IACDE,yBAAyB,EAAE;MAAEJ,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC,sBAAsB;IAAE,CAAC;IACzEI,4BAA4B,EAAE;MAAEL,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC,mBAAmB;IAAE,CAAC;IACzEK,gBAAgB,EAAE;MAChBN,GAAG,EAAEA,CAAA,KAAMC,OAAO,CAAC,sCAAsC;IAC3D;EACF,CAAC,CAAC;AACJ", "ignoreList": []}