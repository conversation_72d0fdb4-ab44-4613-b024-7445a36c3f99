"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */const global=globalThis;function __symbol__(e){return(global.__Zone_symbol_prefix||"__zone_symbol__")+e}function initZone(){const e=global.performance;function t(t){e&&e.mark&&e.mark(t)}function n(t,n){e&&e.measure&&e.measure(t,n)}t("Zone");class o{static{this.__symbol__=__symbol__}static assertZonePatched(){if(global.Promise!==P.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let e=o.current;for(;e.parent;)e=e.parent;return e}static get current(){return O.zone}static get currentTask(){return z}static __load_patch(e,r,s=!1){if(P.hasOwnProperty(e)){const t=!0===global[__symbol__("forceDuplicateZoneCheck")];if(!s&&t)throw Error("Already loaded patch: "+e)}else if(!global["__Zone_disable_"+e]){const s="Zone:"+e;t(s),P[e]=r(global,o,Z),n(s,s)}}get parent(){return this._parent}get name(){return this._name}constructor(e,t){this._parent=e,this._name=t?t.name||"unnamed":"<root>",this._properties=t&&t.properties||{},this._zoneDelegate=new s(this,this._parent&&this._parent._zoneDelegate,t)}get(e){const t=this.getZoneWith(e);if(t)return t._properties[e]}getZoneWith(e){let t=this;for(;t;){if(t._properties.hasOwnProperty(e))return t;t=t._parent}return null}fork(e){if(!e)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,e)}wrap(e,t){if("function"!=typeof e)throw new Error("Expecting function got: "+e);const n=this._zoneDelegate.intercept(this,e,t),o=this;return function(){return o.runGuarded(n,this,arguments,t)}}run(e,t,n,o){O={parent:O,zone:this};try{return this._zoneDelegate.invoke(this,e,t,n,o)}finally{O=O.parent}}runGuarded(e,t=null,n,o){O={parent:O,zone:this};try{try{return this._zoneDelegate.invoke(this,e,t,n,o)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{O=O.parent}}runTask(e,t,n){if(e.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");const o=e,{type:r,data:{isPeriodic:s=!1,isRefreshable:i=!1}={}}=e;if(e.state===m&&(r===w||r===S))return;const a=e.state!=T;a&&o._transitionTo(T,y);const c=z;z=o,O={parent:O,zone:this};try{r!=S||!e.data||s||i||(e.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,o,t,n)}catch(e){if(this._zoneDelegate.handleError(this,e))throw e}}finally{const t=e.state;if(t!==m&&t!==E)if(r==w||s||i&&t===g)a&&o._transitionTo(y,T,g);else{const e=o._zoneDelegates;this._updateTaskCount(o,-1),a&&o._transitionTo(m,T,m),i&&(o._zoneDelegates=e)}O=O.parent,z=c}}scheduleTask(e){if(e.zone&&e.zone!==this){let t=this;for(;t;){if(t===e.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${e.zone.name}`);t=t.parent}}e._transitionTo(g,m);const t=[];e._zoneDelegates=t,e._zone=this;try{e=this._zoneDelegate.scheduleTask(this,e)}catch(t){throw e._transitionTo(E,g,m),this._zoneDelegate.handleError(this,t),t}return e._zoneDelegates===t&&this._updateTaskCount(e,1),e.state==g&&e._transitionTo(y,g),e}scheduleMicroTask(e,t,n,o){return this.scheduleTask(new i(v,e,t,n,o,void 0))}scheduleMacroTask(e,t,n,o,r){return this.scheduleTask(new i(S,e,t,n,o,r))}scheduleEventTask(e,t,n,o,r){return this.scheduleTask(new i(w,e,t,n,o,r))}cancelTask(e){if(e.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(e.zone||k).name+"; Execution: "+this.name+")");if(e.state===y||e.state===T){e._transitionTo(b,y,T);try{this._zoneDelegate.cancelTask(this,e)}catch(t){throw e._transitionTo(E,b),this._zoneDelegate.handleError(this,t),t}return this._updateTaskCount(e,-1),e._transitionTo(m,b),e.runCount=-1,e}}_updateTaskCount(e,t){const n=e._zoneDelegates;-1==t&&(e._zoneDelegates=null);for(let o=0;o<n.length;o++)n[o]._updateTaskCount(e.type,t)}}const r={name:"",onHasTask:(e,t,n,o)=>e.hasTask(n,o),onScheduleTask:(e,t,n,o)=>e.scheduleTask(n,o),onInvokeTask:(e,t,n,o,r,s)=>e.invokeTask(n,o,r,s),onCancelTask:(e,t,n,o)=>e.cancelTask(n,o)};class s{get zone(){return this._zone}constructor(e,t,n){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this._zone=e,this._parentDelegate=t,this._forkZS=n&&(n&&n.onFork?n:t._forkZS),this._forkDlgt=n&&(n.onFork?t:t._forkDlgt),this._forkCurrZone=n&&(n.onFork?this._zone:t._forkCurrZone),this._interceptZS=n&&(n.onIntercept?n:t._interceptZS),this._interceptDlgt=n&&(n.onIntercept?t:t._interceptDlgt),this._interceptCurrZone=n&&(n.onIntercept?this._zone:t._interceptCurrZone),this._invokeZS=n&&(n.onInvoke?n:t._invokeZS),this._invokeDlgt=n&&(n.onInvoke?t:t._invokeDlgt),this._invokeCurrZone=n&&(n.onInvoke?this._zone:t._invokeCurrZone),this._handleErrorZS=n&&(n.onHandleError?n:t._handleErrorZS),this._handleErrorDlgt=n&&(n.onHandleError?t:t._handleErrorDlgt),this._handleErrorCurrZone=n&&(n.onHandleError?this._zone:t._handleErrorCurrZone),this._scheduleTaskZS=n&&(n.onScheduleTask?n:t._scheduleTaskZS),this._scheduleTaskDlgt=n&&(n.onScheduleTask?t:t._scheduleTaskDlgt),this._scheduleTaskCurrZone=n&&(n.onScheduleTask?this._zone:t._scheduleTaskCurrZone),this._invokeTaskZS=n&&(n.onInvokeTask?n:t._invokeTaskZS),this._invokeTaskDlgt=n&&(n.onInvokeTask?t:t._invokeTaskDlgt),this._invokeTaskCurrZone=n&&(n.onInvokeTask?this._zone:t._invokeTaskCurrZone),this._cancelTaskZS=n&&(n.onCancelTask?n:t._cancelTaskZS),this._cancelTaskDlgt=n&&(n.onCancelTask?t:t._cancelTaskDlgt),this._cancelTaskCurrZone=n&&(n.onCancelTask?this._zone:t._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;const o=n&&n.onHasTask;(o||t&&t._hasTaskZS)&&(this._hasTaskZS=o?n:r,this._hasTaskDlgt=t,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=this._zone,n.onScheduleTask||(this._scheduleTaskZS=r,this._scheduleTaskDlgt=t,this._scheduleTaskCurrZone=this._zone),n.onInvokeTask||(this._invokeTaskZS=r,this._invokeTaskDlgt=t,this._invokeTaskCurrZone=this._zone),n.onCancelTask||(this._cancelTaskZS=r,this._cancelTaskDlgt=t,this._cancelTaskCurrZone=this._zone))}fork(e,t){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,e,t):new o(e,t)}intercept(e,t,n){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,e,t,n):t}invoke(e,t,n,o,r){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,e,t,n,o,r):t.apply(n,o)}handleError(e,t){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,e,t)}scheduleTask(e,t){let n=t;if(this._scheduleTaskZS)this._hasTaskZS&&n._zoneDelegates.push(this._hasTaskDlgtOwner),n=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,e,t),n||(n=t);else if(t.scheduleFn)t.scheduleFn(t);else{if(t.type!=v)throw new Error("Task is missing scheduleFn.");_(t)}return n}invokeTask(e,t,n,o){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,e,t,n,o):t.callback.apply(n,o)}cancelTask(e,t){let n;if(this._cancelTaskZS)n=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,e,t);else{if(!t.cancelFn)throw Error("Task is not cancelable");n=t.cancelFn(t)}return n}hasTask(e,t){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,e,t)}catch(t){this.handleError(e,t)}}_updateTaskCount(e,t){const n=this._taskCounts,o=n[e],r=n[e]=o+t;if(r<0)throw new Error("More tasks executed then were scheduled.");0!=o&&0!=r||this.hasTask(this._zone,{microTask:n.microTask>0,macroTask:n.macroTask>0,eventTask:n.eventTask>0,change:e})}}class i{constructor(e,t,n,o,r,s){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=e,this.source=t,this.data=o,this.scheduleFn=r,this.cancelFn=s,!n)throw new Error("callback is not defined");this.callback=n;const a=this;this.invoke=e===w&&o&&o.useG?i.invokeTask:function(){return i.invokeTask.call(global,a,this,arguments)}}static invokeTask(e,t,n){e||(e=this),D++;try{return e.runCount++,e.zone.runTask(e,t,n)}finally{1==D&&d(),D--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(m,g)}_transitionTo(e,t,n){if(this._state!==t&&this._state!==n)throw new Error(`${this.type} '${this.source}': can not transition to '${e}', expecting state '${t}'${n?" or '"+n+"'":""}, was '${this._state}'.`);this._state=e,e==m&&(this._zoneDelegates=null)}toString(){return this.data&&void 0!==this.data.handleId?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}const a=__symbol__("setTimeout"),c=__symbol__("Promise"),l=__symbol__("then");let h,u=[],p=!1;function f(e){if(h||global[c]&&(h=global[c].resolve(0)),h){let t=h[l];t||(t=h.then),t.call(h,e)}else global[a](e,0)}function _(e){0===D&&0===u.length&&f(d),e&&u.push(e)}function d(){if(!p){for(p=!0;u.length;){const e=u;u=[];for(let t=0;t<e.length;t++){const n=e[t];try{n.zone.runTask(n,null,null)}catch(e){Z.onUnhandledError(e)}}}Z.microtaskDrainDone(),p=!1}}const k={name:"NO ZONE"},m="notScheduled",g="scheduling",y="scheduled",T="running",b="canceling",E="unknown",v="microTask",S="macroTask",w="eventTask",P={},Z={symbol:__symbol__,currentZoneFrame:()=>O,onUnhandledError:N,microtaskDrainDone:N,scheduleMicroTask:_,showUncaughtError:()=>!o[__symbol__("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:N,patchMethod:()=>N,bindArguments:()=>[],patchThen:()=>N,patchMacroTask:()=>N,patchEventPrototype:()=>N,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>N,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>N,wrapWithCurrentZone:()=>N,filterProperties:()=>[],attachOriginToPatched:()=>N,_redefineProperty:()=>N,patchCallbacks:()=>N,nativeScheduleMicroTask:f};let O={parent:null,zone:new o(null,null)},z=null,D=0;function N(){}return n("Zone","Zone"),o}const ObjectGetOwnPropertyDescriptor=Object.getOwnPropertyDescriptor,ObjectDefineProperty=Object.defineProperty,ObjectGetPrototypeOf=Object.getPrototypeOf,ArraySlice=Array.prototype.slice,ADD_EVENT_LISTENER_STR="addEventListener",REMOVE_EVENT_LISTENER_STR="removeEventListener",TRUE_STR="true",FALSE_STR="false",ZONE_SYMBOL_PREFIX=__symbol__("");function wrapWithCurrentZone(e,t){return Zone.current.wrap(e,t)}function scheduleMacroTaskWithCurrentZone(e,t,n,o,r){return Zone.current.scheduleMacroTask(e,t,n,o,r)}const zoneSymbol=__symbol__,isWindowExists="undefined"!=typeof window,internalWindow=isWindowExists?window:void 0,_global=isWindowExists&&internalWindow||globalThis,REMOVE_ATTRIBUTE="removeAttribute";function bindArguments(e,t){for(let n=e.length-1;n>=0;n--)"function"==typeof e[n]&&(e[n]=wrapWithCurrentZone(e[n],t+"_"+n));return e}function isPropertyWritable(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&void 0===e.set)}const isWebWorker="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,isNode=!("nw"in _global)&&void 0!==_global.process&&"[object process]"===_global.process.toString(),isBrowser=!isNode&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),isMix=void 0!==_global.process&&"[object process]"===_global.process.toString()&&!isWebWorker&&!(!isWindowExists||!internalWindow.HTMLElement),zoneSymbolEventNames$1={},enableBeforeunloadSymbol=zoneSymbol("enable_beforeunload"),wrapFn=function(e){if(!(e=e||_global.event))return;let t=zoneSymbolEventNames$1[e.type];t||(t=zoneSymbolEventNames$1[e.type]=zoneSymbol("ON_PROPERTY"+e.type));const n=this||e.target||_global,o=n[t];let r;return isBrowser&&n===internalWindow&&"error"===e.type?(r=o&&o.call(this,e.message,e.filename,e.lineno,e.colno,e.error),!0===r&&e.preventDefault()):(r=o&&o.apply(this,arguments),"beforeunload"===e.type&&_global[enableBeforeunloadSymbol]&&"string"==typeof r?e.returnValue=r:null==r||r||e.preventDefault()),r};function patchProperty(e,t,n){let o=ObjectGetOwnPropertyDescriptor(e,t);if(!o&&n&&ObjectGetOwnPropertyDescriptor(n,t)&&(o={enumerable:!0,configurable:!0}),!o||!o.configurable)return;const r=zoneSymbol("on"+t+"patched");if(e.hasOwnProperty(r)&&e[r])return;delete o.writable,delete o.value;const s=o.get,i=o.set,a=t.slice(2);let c=zoneSymbolEventNames$1[a];c||(c=zoneSymbolEventNames$1[a]=zoneSymbol("ON_PROPERTY"+a)),o.set=function(t){let n=this;n||e!==_global||(n=_global),n&&("function"==typeof n[c]&&n.removeEventListener(a,wrapFn),i&&i.call(n,null),n[c]=t,"function"==typeof t&&n.addEventListener(a,wrapFn,!1))},o.get=function(){let n=this;if(n||e!==_global||(n=_global),!n)return null;const r=n[c];if(r)return r;if(s){let e=s.call(this);if(e)return o.set.call(this,e),"function"==typeof n[REMOVE_ATTRIBUTE]&&n.removeAttribute(t),e}return null},ObjectDefineProperty(e,t,o),e[r]=!0}function patchOnProperties(e,t,n){if(t)for(let o=0;o<t.length;o++)patchProperty(e,"on"+t[o],n);else{const t=[];for(const n in e)"on"==n.slice(0,2)&&t.push(n);for(let o=0;o<t.length;o++)patchProperty(e,t[o],n)}}function copySymbolProperties(e,t){"function"==typeof Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((n=>{const o=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,{get:function(){return e[n]},set:function(t){(!o||o.writable&&"function"==typeof o.set)&&(e[n]=t)},enumerable:!o||o.enumerable,configurable:!o||o.configurable})}))}let shouldCopySymbolProperties=!1;function setShouldCopySymbolProperties(e){shouldCopySymbolProperties=e}function patchMethod(e,t,n){let o=e;for(;o&&!o.hasOwnProperty(t);)o=ObjectGetPrototypeOf(o);!o&&e[t]&&(o=e);const r=zoneSymbol(t);let s=null;if(o&&(!(s=o[r])||!o.hasOwnProperty(r))&&(s=o[r]=o[t],isPropertyWritable(o&&ObjectGetOwnPropertyDescriptor(o,t)))){const e=n(s,r,t);o[t]=function(){return e(this,arguments)},attachOriginToPatched(o[t],s),shouldCopySymbolProperties&&copySymbolProperties(s,o[t])}return s}function patchMacroTask(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=patchMethod(e,t,(e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?scheduleMacroTaskWithCurrentZone(s.name,o[s.cbIdx],s,r):e.apply(t,o)}))}function patchMicroTask(e,t,n){let o=null;function r(e){const t=e.data;return t.args[t.cbIdx]=function(){e.invoke.apply(this,arguments)},o.apply(t.target,t.args),e}o=patchMethod(e,t,(e=>function(t,o){const s=n(t,o);return s.cbIdx>=0&&"function"==typeof o[s.cbIdx]?Zone.current.scheduleMicroTask(s.name,o[s.cbIdx],s,r):e.apply(t,o)}))}function attachOriginToPatched(e,t){e[zoneSymbol("OriginalDelegate")]=t}function isFunction(e){return"function"==typeof e}function isNumber(e){return"number"==typeof e}function patchPromise(e){e.__load_patch("ZoneAwarePromise",((e,t,n)=>{const o=Object.getOwnPropertyDescriptor,r=Object.defineProperty,s=n.symbol,i=[],a=!1!==e[s("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],c=s("Promise"),l=s("then"),h="__creationTrace__";n.onUnhandledError=e=>{if(n.showUncaughtError()){const t=e&&e.rejection;t?console.error("Unhandled Promise rejection:",t instanceof Error?t.message:t,"; Zone:",e.zone.name,"; Task:",e.task&&e.task.source,"; Value:",t,t instanceof Error?t.stack:void 0):console.error(e)}},n.microtaskDrainDone=()=>{for(;i.length;){const e=i.shift();try{e.zone.runGuarded((()=>{if(e.throwOriginal)throw e.rejection;throw e}))}catch(e){p(e)}}};const u=s("unhandledPromiseRejectionHandler");function p(e){n.onUnhandledError(e);try{const n=t[u];"function"==typeof n&&n.call(this,e)}catch(e){}}function f(e){return e&&e.then}function _(e){return e}function d(e){return I.reject(e)}const k=s("state"),m=s("value"),g=s("finally"),y=s("parentPromiseValue"),T=s("parentPromiseState"),b="Promise.then",E=null,v=!0,S=!1,w=0;function P(e,t){return n=>{try{D(e,t,n)}catch(t){D(e,!1,t)}}}const Z=function(){let e=!1;return function t(n){return function(){e||(e=!0,n.apply(null,arguments))}}},O="Promise resolved with itself",z=s("currentTaskTrace");function D(e,o,s){const c=Z();if(e===s)throw new TypeError(O);if(e[k]===E){let l=null;try{"object"!=typeof s&&"function"!=typeof s||(l=s&&s.then)}catch(t){return c((()=>{D(e,!1,t)}))(),e}if(o!==S&&s instanceof I&&s.hasOwnProperty(k)&&s.hasOwnProperty(m)&&s[k]!==E)R(s),D(e,s[k],s[m]);else if(o!==S&&"function"==typeof l)try{l.call(s,c(P(e,o)),c(P(e,!1)))}catch(t){c((()=>{D(e,!1,t)}))()}else{e[k]=o;const c=e[m];if(e[m]=s,e[g]===g&&o===v&&(e[k]=e[T],e[m]=e[y]),o===S&&s instanceof Error){const e=t.currentTask&&t.currentTask.data&&t.currentTask.data[h];e&&r(s,z,{configurable:!0,enumerable:!1,writable:!0,value:e})}for(let t=0;t<c.length;)C(e,c[t++],c[t++],c[t++],c[t++]);if(0==c.length&&o==S){e[k]=w;let o=s;try{throw new Error("Uncaught (in promise): "+function e(t){return t&&t.toString===Object.prototype.toString?(t.constructor&&t.constructor.name||"")+": "+JSON.stringify(t):t?t.toString():Object.prototype.toString.call(t)}(s)+(s&&s.stack?"\n"+s.stack:""))}catch(e){o=e}a&&(o.throwOriginal=!0),o.rejection=s,o.promise=e,o.zone=t.current,o.task=t.currentTask,i.push(o),n.scheduleMicroTask()}}}return e}const N=s("rejectionHandledHandler");function R(e){if(e[k]===w){try{const n=t[N];n&&"function"==typeof n&&n.call(this,{rejection:e[m],promise:e})}catch(e){}e[k]=S;for(let t=0;t<i.length;t++)e===i[t].promise&&i.splice(t,1)}}function C(e,t,n,o,r){R(e);const s=e[k],i=s?"function"==typeof o?o:_:"function"==typeof r?r:d;t.scheduleMicroTask(b,(()=>{try{const o=e[m],r=!!n&&g===n[g];r&&(n[y]=o,n[T]=s);const a=t.run(i,void 0,r&&i!==d&&i!==_?[]:[o]);D(n,!0,a)}catch(e){D(n,!1,e)}}),n)}const j=function(){},M=e.AggregateError;class I{static toString(){return"function ZoneAwarePromise() { [native code] }"}static resolve(e){return e instanceof I?e:D(new this(null),v,e)}static reject(e){return D(new this(null),S,e)}static withResolvers(){const e={};return e.promise=new I(((t,n)=>{e.resolve=t,e.reject=n})),e}static any(e){if(!e||"function"!=typeof e[Symbol.iterator])return Promise.reject(new M([],"All promises were rejected"));const t=[];let n=0;try{for(let o of e)n++,t.push(I.resolve(o))}catch(e){return Promise.reject(new M([],"All promises were rejected"))}if(0===n)return Promise.reject(new M([],"All promises were rejected"));let o=!1;const r=[];return new I(((e,s)=>{for(let i=0;i<t.length;i++)t[i].then((t=>{o||(o=!0,e(t))}),(e=>{r.push(e),n--,0===n&&(o=!0,s(new M(r,"All promises were rejected")))}))}))}static race(e){let t,n,o=new this(((e,o)=>{t=e,n=o}));function r(e){t(e)}function s(e){n(e)}for(let t of e)f(t)||(t=this.resolve(t)),t.then(r,s);return o}static all(e){return I.allWithCallback(e)}static allSettled(e){return(this&&this.prototype instanceof I?this:I).allWithCallback(e,{thenCallback:e=>({status:"fulfilled",value:e}),errorCallback:e=>({status:"rejected",reason:e})})}static allWithCallback(e,t){let n,o,r=new this(((e,t)=>{n=e,o=t})),s=2,i=0;const a=[];for(let r of e){f(r)||(r=this.resolve(r));const e=i;try{r.then((o=>{a[e]=t?t.thenCallback(o):o,s--,0===s&&n(a)}),(r=>{t?(a[e]=t.errorCallback(r),s--,0===s&&n(a)):o(r)}))}catch(e){o(e)}s++,i++}return s-=2,0===s&&n(a),r}constructor(e){const t=this;if(!(t instanceof I))throw new Error("Must be an instanceof Promise.");t[k]=E,t[m]=[];try{const n=Z();e&&e(n(P(t,v)),n(P(t,S)))}catch(e){D(t,!1,e)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return I}then(e,n){let o=this.constructor?.[Symbol.species];o&&"function"==typeof o||(o=this.constructor||I);const r=new o(j),s=t.current;return this[k]==E?this[m].push(s,r,e,n):C(this,s,r,e,n),r}catch(e){return this.then(null,e)}finally(e){let n=this.constructor?.[Symbol.species];n&&"function"==typeof n||(n=I);const o=new n(j);o[g]=g;const r=t.current;return this[k]==E?this[m].push(r,o,e,e):C(this,r,o,e,e),o}}I.resolve=I.resolve,I.reject=I.reject,I.race=I.race,I.all=I.all;const A=e[c]=e.Promise;e.Promise=I;const L=s("thenPatched");function F(e){const t=e.prototype,n=o(t,"then");if(n&&(!1===n.writable||!n.configurable))return;const r=t.then;t[l]=r,e.prototype.then=function(e,t){return new I(((e,t)=>{r.call(this,e,t)})).then(e,t)},e[L]=!0}return n.patchThen=F,A&&(F(A),patchMethod(e,"fetch",(e=>function t(e){return function(t,n){let o=e.apply(t,n);if(o instanceof I)return o;let r=o.constructor;return r[L]||F(r),o}}(e)))),Promise[t.__symbol__("uncaughtPromiseErrors")]=i,I}))}function patchToString(e){e.__load_patch("toString",(e=>{const t=Function.prototype.toString,n=zoneSymbol("OriginalDelegate"),o=zoneSymbol("Promise"),r=zoneSymbol("Error"),s=function s(){if("function"==typeof this){const s=this[n];if(s)return"function"==typeof s?t.call(s):Object.prototype.toString.call(s);if(this===Promise){const n=e[o];if(n)return t.call(n)}if(this===Error){const n=e[r];if(n)return t.call(n)}}return t.call(this)};s[n]=t,Function.prototype.toString=s;const i=Object.prototype.toString;Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?"[object Promise]":i.call(this)}}))}function loadZone(){const e=globalThis,t=!0===e[__symbol__("forceDuplicateZoneCheck")];if(e.Zone&&(t||"function"!=typeof e.Zone.__symbol__))throw new Error("Zone already loaded.");return e.Zone??=initZone(),e.Zone}let passiveSupported=!1;if("undefined"!=typeof window)try{const e=Object.defineProperty({},"passive",{get:function(){passiveSupported=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){passiveSupported=!1}const OPTIMIZED_ZONE_EVENT_TASK_DATA={useG:!0},zoneSymbolEventNames={},globalSources={},EVENT_NAME_SYMBOL_REGX=new RegExp("^"+ZONE_SYMBOL_PREFIX+"(\\w+)(true|false)$"),IMMEDIATE_PROPAGATION_SYMBOL=zoneSymbol("propagationStopped");function prepareEventNames(e,t){const n=(t?t(e):e)+FALSE_STR,o=(t?t(e):e)+TRUE_STR,r=ZONE_SYMBOL_PREFIX+n,s=ZONE_SYMBOL_PREFIX+o;zoneSymbolEventNames[e]={},zoneSymbolEventNames[e][FALSE_STR]=r,zoneSymbolEventNames[e][TRUE_STR]=s}function patchEventTarget(e,t,n,o){const r=o&&o.add||ADD_EVENT_LISTENER_STR,s=o&&o.rm||REMOVE_EVENT_LISTENER_STR,i=o&&o.listeners||"eventListeners",a=o&&o.rmAll||"removeAllListeners",c=zoneSymbol(r),l="."+r+":",h="prependListener",u="."+h+":",p=function(e,t,n){if(e.isRemoved)return;const o=e.callback;let r;"object"==typeof o&&o.handleEvent&&(e.callback=e=>o.handleEvent(e),e.originalDelegate=o);try{e.invoke(e,t,[n])}catch(e){r=e}const i=e.options;return i&&"object"==typeof i&&i.once&&t[s].call(t,n.type,e.originalDelegate?e.originalDelegate:e.callback,i),r};function f(n,o,r){if(!(o=o||e.event))return;const s=n||o.target||e,i=s[zoneSymbolEventNames[o.type][r?TRUE_STR:FALSE_STR]];if(i){const e=[];if(1===i.length){const t=p(i[0],s,o);t&&e.push(t)}else{const t=i.slice();for(let n=0;n<t.length&&(!o||!0!==o[IMMEDIATE_PROPAGATION_SYMBOL]);n++){const r=p(t[n],s,o);r&&e.push(r)}}if(1===e.length)throw e[0];for(let n=0;n<e.length;n++){const o=e[n];t.nativeScheduleMicroTask((()=>{throw o}))}}}const _=function(e){return f(this,e,!1)},d=function(e){return f(this,e,!0)};function k(t,n){if(!t)return!1;let o=!0;n&&void 0!==n.useG&&(o=n.useG);const p=n&&n.vh;let f=!0;n&&void 0!==n.chkDup&&(f=n.chkDup);let k=!1;n&&void 0!==n.rt&&(k=n.rt);let m=t;for(;m&&!m.hasOwnProperty(r);)m=ObjectGetPrototypeOf(m);if(!m&&t[r]&&(m=t),!m)return!1;if(m[c])return!1;const g=n&&n.eventNameToString,y={},T=m[c]=m[r],b=m[zoneSymbol(s)]=m[s],E=m[zoneSymbol(i)]=m[i],v=m[zoneSymbol(a)]=m[a];let S;n&&n.prepend&&(S=m[zoneSymbol(n.prepend)]=m[n.prepend]);const w=o?function(e){if(!y.isExisting)return T.call(y.target,y.eventName,y.capture?d:_,y.options)}:function(e){return T.call(y.target,y.eventName,e.invoke,y.options)},P=o?function(e){if(!e.isRemoved){const t=zoneSymbolEventNames[e.eventName];let n;t&&(n=t[e.capture?TRUE_STR:FALSE_STR]);const o=n&&e.target[n];if(o)for(let t=0;t<o.length;t++)if(o[t]===e){o.splice(t,1),e.isRemoved=!0,e.removeAbortListener&&(e.removeAbortListener(),e.removeAbortListener=null),0===o.length&&(e.allRemoved=!0,e.target[n]=null);break}}if(e.allRemoved)return b.call(e.target,e.eventName,e.capture?d:_,e.options)}:function(e){return b.call(e.target,e.eventName,e.invoke,e.options)},Z=n&&n.diff?n.diff:function(e,t){const n=typeof t;return"function"===n&&e.callback===t||"object"===n&&e.originalDelegate===t},O=Zone[zoneSymbol("UNPATCHED_EVENTS")],z=e[zoneSymbol("PASSIVE_EVENTS")],D=function(t,r,s,i,a=!1,c=!1){return function(){const l=this||e;let h=arguments[0];n&&n.transferEventName&&(h=n.transferEventName(h));let u=arguments[1];if(!u)return t.apply(this,arguments);if(isNode&&"uncaughtException"===h)return t.apply(this,arguments);let _=!1;if("function"!=typeof u){if(!u.handleEvent)return t.apply(this,arguments);_=!0}if(p&&!p(t,u,l,arguments))return;const d=passiveSupported&&!!z&&-1!==z.indexOf(h),k=function T(e){if("object"==typeof e&&null!==e){const t={...e};return e.signal&&(t.signal=e.signal),t}return e}(function m(e,t){return!passiveSupported&&"object"==typeof e&&e?!!e.capture:passiveSupported&&t?"boolean"==typeof e?{capture:e,passive:!0}:e?"object"==typeof e&&!1!==e.passive?{...e,passive:!0}:e:{passive:!0}:e}(arguments[2],d)),b=k?.signal;if(b?.aborted)return;if(O)for(let e=0;e<O.length;e++)if(h===O[e])return d?t.call(l,h,u,k):t.apply(this,arguments);const E=!!k&&("boolean"==typeof k||k.capture),v=!(!k||"object"!=typeof k)&&k.once,S=Zone.current;let w=zoneSymbolEventNames[h];w||(prepareEventNames(h,g),w=zoneSymbolEventNames[h]);const P=w[E?TRUE_STR:FALSE_STR];let D,N=l[P],R=!1;if(N){if(R=!0,f)for(let e=0;e<N.length;e++)if(Z(N[e],u))return}else N=l[P]=[];const C=l.constructor.name,j=globalSources[C];j&&(D=j[h]),D||(D=C+r+(g?g(h):h)),y.options=k,v&&(y.options.once=!1),y.target=l,y.capture=E,y.eventName=h,y.isExisting=R;const M=o?OPTIMIZED_ZONE_EVENT_TASK_DATA:void 0;M&&(M.taskData=y),b&&(y.options.signal=void 0);const I=S.scheduleEventTask(D,u,M,s,i);if(b){y.options.signal=b;const e=()=>I.zone.cancelTask(I);t.call(b,"abort",e,{once:!0}),I.removeAbortListener=()=>b.removeEventListener("abort",e)}return y.target=null,M&&(M.taskData=null),v&&(y.options.once=!0),(passiveSupported||"boolean"!=typeof I.options)&&(I.options=k),I.target=l,I.capture=E,I.eventName=h,_&&(I.originalDelegate=u),c?N.unshift(I):N.push(I),a?l:void 0}};return m[r]=D(T,l,w,P,k),S&&(m[h]=D(S,u,(function(e){return S.call(y.target,y.eventName,e.invoke,y.options)}),P,k,!0)),m[s]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=arguments[2],s=!!r&&("boolean"==typeof r||r.capture),i=arguments[1];if(!i)return b.apply(this,arguments);if(p&&!p(b,i,t,arguments))return;const a=zoneSymbolEventNames[o];let c;a&&(c=a[s?TRUE_STR:FALSE_STR]);const l=c&&t[c];if(l)for(let e=0;e<l.length;e++){const n=l[e];if(Z(n,i))return l.splice(e,1),n.isRemoved=!0,0!==l.length||(n.allRemoved=!0,t[c]=null,s||"string"!=typeof o)||(t[ZONE_SYMBOL_PREFIX+"ON_PROPERTY"+o]=null),n.zone.cancelTask(n),k?t:void 0}return b.apply(this,arguments)},m[i]=function(){const t=this||e;let o=arguments[0];n&&n.transferEventName&&(o=n.transferEventName(o));const r=[],s=findEventTasks(t,g?g(o):o);for(let e=0;e<s.length;e++){const t=s[e];r.push(t.originalDelegate?t.originalDelegate:t.callback)}return r},m[a]=function(){const t=this||e;let o=arguments[0];if(o){n&&n.transferEventName&&(o=n.transferEventName(o));const e=zoneSymbolEventNames[o];if(e){const n=t[e[FALSE_STR]],r=t[e[TRUE_STR]];if(n){const e=n.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}if(r){const e=r.slice();for(let t=0;t<e.length;t++){const n=e[t];this[s].call(this,o,n.originalDelegate?n.originalDelegate:n.callback,n.options)}}}}else{const e=Object.keys(t);for(let t=0;t<e.length;t++){const n=EVENT_NAME_SYMBOL_REGX.exec(e[t]);let o=n&&n[1];o&&"removeListener"!==o&&this[a].call(this,o)}this[a].call(this,"removeListener")}if(k)return this},attachOriginToPatched(m[r],T),attachOriginToPatched(m[s],b),v&&attachOriginToPatched(m[a],v),E&&attachOriginToPatched(m[i],E),!0}let m=[];for(let e=0;e<n.length;e++)m[e]=k(n[e],o);return m}function findEventTasks(e,t){if(!t){const n=[];for(let o in e){const r=EVENT_NAME_SYMBOL_REGX.exec(o);let s=r&&r[1];if(s&&(!t||s===t)){const t=e[o];if(t)for(let e=0;e<t.length;e++)n.push(t[e])}}return n}let n=zoneSymbolEventNames[t];n||(prepareEventNames(t),n=zoneSymbolEventNames[t]);const o=e[n[FALSE_STR]],r=e[n[TRUE_STR]];return o?r?o.concat(r):o.slice():r?r.slice():[]}function patchQueueMicrotask(e,t){t.patchMethod(e,"queueMicrotask",(e=>function(e,t){Zone.current.scheduleMicroTask("queueMicrotask",t[0])}))}const taskSymbol=zoneSymbol("zoneTask");function patchTimer(e,t,n,o){let r=null,s=null;n+=o;const i={};function a(t){const n=t.data;n.args[0]=function(){return t.invoke.apply(this,arguments)};const o=r.apply(e,n.args);return isNumber(o)?n.handleId=o:(n.handle=o,n.isRefreshable=isFunction(o.refresh)),t}function c(t){const{handle:n,handleId:o}=t.data;return s.call(e,n??o)}r=patchMethod(e,t+=o,(n=>function(r,s){if(isFunction(s[0])){const e={isRefreshable:!1,isPeriodic:"Interval"===o,delay:"Timeout"===o||"Interval"===o?s[1]||0:void 0,args:s},n=s[0];s[0]=function t(){try{return n.apply(this,arguments)}finally{const{handle:t,handleId:n,isPeriodic:o,isRefreshable:r}=e;o||r||(n?delete i[n]:t&&(t[taskSymbol]=null))}};const r=scheduleMacroTaskWithCurrentZone(t,s[0],e,a,c);if(!r)return r;const{handleId:l,handle:h,isRefreshable:u,isPeriodic:p}=r.data;if(l)i[l]=r;else if(h&&(h[taskSymbol]=r,u&&!p)){const e=h.refresh;h.refresh=function(){const{zone:t,state:n}=r;return"notScheduled"===n?(r._state="scheduled",t._updateTaskCount(r,1)):"running"===n&&(r._state="scheduling"),e.call(this)}}return h??l??r}return n.apply(e,s)})),s=patchMethod(e,n,(t=>function(n,o){const r=o[0];let s;isNumber(r)?(s=i[r],delete i[r]):(s=r?.[taskSymbol],s?r[taskSymbol]=null:s=r),s?.type?s.cancelFn&&s.zone.cancelTask(s):t.apply(e,o)}))}function patchEvents(e){e.__load_patch("EventEmitter",((e,t,n)=>{const o="addListener",r="removeListener",s=function(e,t){return e.callback===t||e.callback.listener===t},i=function(e){return"string"==typeof e?e:e?e.toString().replace("(","_").replace(")","_"):""};let a;try{a=require("events")}catch(e){}a&&a.EventEmitter&&function c(t){const a=patchEventTarget(e,n,[t],{useG:!1,add:o,rm:r,prepend:"prependListener",rmAll:"removeAllListeners",listeners:"listeners",chkDup:!1,rt:!0,diff:s,eventNameToString:i});a&&a[0]&&(t.on=t[o],t.off=t[r])}(a.EventEmitter.prototype)}))}function patchFs(e){e.__load_patch("fs",((e,t,n)=>{let o;try{o=require("fs")}catch(e){}if(!o)return;["access","appendFile","chmod","chown","close","exists","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","lutimes","link","lstat","mkdir","mkdtemp","open","opendir","read","readdir","readFile","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","write","writeFile","writev"].filter((e=>!!o[e]&&"function"==typeof o[e])).forEach((e=>{patchMacroTask(o,e,((t,n)=>({name:"fs."+e,args:n,cbIdx:n.length>0?n.length-1:-1,target:t})))}));const r=o.realpath?.[n.symbol("OriginalDelegate")];r?.native&&(o.realpath.native=r.native,patchMacroTask(o.realpath,"native",((e,t)=>({args:t,target:e,cbIdx:t.length>0?t.length-1:-1,name:"fs.realpath.native"}))))}))}function patchNodeUtil(e){e.__load_patch("node_util",((e,t,n)=>{n.patchOnProperties=patchOnProperties,n.patchMethod=patchMethod,n.bindArguments=bindArguments,n.patchMacroTask=patchMacroTask,setShouldCopySymbolProperties(!0)}))}const set="set",clear="clear";function patchNode(e){patchNodeUtil(e),patchEvents(e),patchFs(e),e.__load_patch("node_timers",((e,t)=>{let n=!1;try{const t=require("timers");if(e.setTimeout!==t.setTimeout&&!isMix){const o=t.setTimeout;t.setTimeout=function(){return n=!0,o.apply(this,arguments)};const r=e.setTimeout((()=>{}),100);clearTimeout(r),t.setTimeout=o}patchTimer(t,set,clear,"Timeout"),patchTimer(t,set,clear,"Interval"),patchTimer(t,set,clear,"Immediate")}catch(e){}isMix||(n?(e[t.__symbol__("setTimeout")]=e.setTimeout,e[t.__symbol__("setInterval")]=e.setInterval,e[t.__symbol__("setImmediate")]=e.setImmediate):(patchTimer(e,set,clear,"Timeout"),patchTimer(e,set,clear,"Interval"),patchTimer(e,set,clear,"Immediate")))})),e.__load_patch("nextTick",(()=>{patchMicroTask(process,"nextTick",((e,t)=>({name:"process.nextTick",args:t,cbIdx:t.length>0&&"function"==typeof t[0]?0:-1,target:process})))})),e.__load_patch("handleUnhandledPromiseRejection",((e,t,n)=>{function o(e){return function(t){findEventTasks(process,e).forEach((n=>{"unhandledRejection"===e?n.invoke(t.rejection,t.promise):"rejectionHandled"===e&&n.invoke(t.promise)}))}}t[n.symbol("unhandledPromiseRejectionHandler")]=o("unhandledRejection"),t[n.symbol("rejectionHandledHandler")]=o("rejectionHandled")})),e.__load_patch("crypto",(()=>{let e;try{e=require("crypto")}catch(e){}e&&["randomBytes","pbkdf2"].forEach((t=>{patchMacroTask(e,t,((n,o)=>({name:"crypto."+t,args:o,cbIdx:o.length>0&&"function"==typeof o[o.length-1]?o.length-1:-1,target:e})))}))})),e.__load_patch("console",((e,t)=>{["dir","log","info","error","warn","assert","debug","timeEnd","trace"].forEach((e=>{const n=console[t.__symbol__(e)]=console[e];n&&(console[e]=function(){const e=ArraySlice.call(arguments);return t.current===t.root?n.apply(this,e):t.root.run(n,this,e)})}))})),e.__load_patch("queueMicrotask",((e,t,n)=>{patchQueueMicrotask(e,n)}))}function rollupMain(){const e=loadZone();return patchNode(e),patchPromise(e),patchToString(e),e}rollupMain();