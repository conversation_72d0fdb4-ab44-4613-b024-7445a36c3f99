{"name": "bonjour-service", "version": "1.3.0", "description": "A Bonjour/Zeroconf implementation in TypeScript", "main": "./dist/index.js", "types": "./dist/index.d.ts", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}, "devDependencies": {"@types/node": "^22.9.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "after-all": "^2.0.2", "eslint": "^8.57.1", "standard": "^17.1.2", "tape": "^5.9.0", "ts-node-dev": "^2.0.0", "typescript": "^5.6.3"}, "scripts": {"test": "standard --fix && tape test/*.js", "build": "tsc --removeComments true", "dev": "ts-node-dev --respawn --transpile-only ./src/index.ts", "example:simple": "node examples/simple"}, "repository": {"type": "git", "url": "https://github.com/onlxltd/bonjour-service.git"}, "keywords": ["bonjour", "zeroconf", "zero", "configuration", "mdns", "dns", "service", "discovery", "multicast", "broadcast", "dns-sd"], "author": "ON LX Lited <<EMAIL>> (https://onlx.ltd)", "license": "MIT", "bugs": {"url": "https://github.com/onlxltd/bonjour-service/issues"}, "homepage": "https://github.com/onlxltd/bonjour-service", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}