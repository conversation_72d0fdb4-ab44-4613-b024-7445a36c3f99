{"name": "karma-coverage", "version": "2.2.1", "description": "A Karma plugin. Generate code coverage.", "main": "lib/index.js", "scripts": {"lint": "eslint **/*.js", "test": "mocha", "update-contributors": "grunt contributors", "examples": "grunt karma", "semantic-release": "semantic-release", "release": "npm run update-contributors && semantic-release", "commitlint": "commitlint"}, "repository": {"type": "git", "url": "git://github.com/karma-runner/karma-coverage.git"}, "keywords": ["karma-plugin", "karma-preprocessor", "karma-reporter", "coverage", "istanbul"], "author": "SATO taichi <<EMAIL>>", "dependencies": {"istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^5.1.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.0.5", "minimatch": "^3.0.4"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^8.3.6", "@commitlint/config-conventional": "^8.3.6", "@commitlint/travis-cli": "^8.3.6", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@semantic-release/npm": "^7.0.5", "chai": "^4.3.4", "coffeescript": "^2.6.1", "eslint": "^6.5.1", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-standard": "^4.1.0", "grunt": "^1.4.1", "grunt-cli": "^1.4.3", "grunt-karma": "^3.0.2", "grunt-npm": "^0.0.2", "husky": "^4.3.8", "ibrik": "^2.0.0", "karma": "^4.2.0", "karma-coffee-preprocessor": "^1.0.1", "karma-firefox-launcher": "1.x || ^0.1.6", "karma-mocha": "^2.0.1", "karma-requirejs": "1.x || ^0.2.2", "mocha": "^7.2.0", "mocks": "0.0.15", "requirejs": "^2.1.20", "semantic-release": "^17.0.1", "sinon": "^7.2.7", "sinon-chai": "^3.7.0"}, "engines": {"node": ">=10.0.0"}, "mocha": {"ui": "bdd", "require": ["test/mocha-globals.js"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "contributors": ["dignifiedquire <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Aymeric <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "johnj<PERSON>ton <<EMAIL>>", "dependabot[bot] <49699333+dependabot[bot]@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "hicom150 <<EMAIL>>", "semantic-release-bot <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <nmala<PERSON><PERSON>@palantir.com>", "<PERSON> <<EMAIL>>", "nicojs <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <Andrew<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <g<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON>.no <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <nick.mat<PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <robin<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON>, <PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "abbr <<EMAIL>>", "aprooks <<EMAIL>>", "carlos <<EMAIL>>", "fbergr <<EMAIL>>", "piecyk <<EMAIL>>", "terussell85 <<EMAIL>>"]}