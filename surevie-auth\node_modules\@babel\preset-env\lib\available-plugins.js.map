{"version": 3, "names": ["_pluginSyntaxImportAssertions", "require", "_pluginSyntaxImportAttributes", "_pluginTransformAsyncGeneratorFunctions", "_pluginTransformAsyncToGenerator", "_pluginTransformArrowFunctions", "_pluginTransformBlockScopedFunctions", "_pluginTransformBlockScoping", "_pluginTransformClasses", "_pluginTransformClassProperties", "_pluginTransformClassStaticBlock", "_pluginTransformComputedProperties", "_pluginTransformDestructuring", "_pluginTransformDotallRegex", "_pluginTransformDuplicateKeys", "_pluginTransformDuplicateNamedCapturingGroupsRegex", "_pluginTransformDynamicImport", "_pluginTransformExponentiationOperator", "_pluginTransformExportNamespaceFrom", "_pluginTransformForOf", "_pluginTransformFunctionName", "_pluginTransformJsonStrings", "_pluginTransformLiterals", "_pluginTransformLogicalAssignmentOperators", "_pluginTransformMemberExpressionLiterals", "_pluginTransformModulesAmd", "_pluginTransformModulesCommonjs", "_pluginTransformModulesSystemjs", "_pluginTransformModulesUmd", "_pluginTransformNamedCapturingGroupsRegex", "_pluginTransformNewTarget", "_pluginTransformNullishCoalescingOperator", "_pluginTransformNumericSeparator", "_pluginTransformObjectRestSpread", "_pluginTransformObjectSuper", "_pluginTransformOptionalCatchBinding", "_pluginTransformOptionalChaining", "_pluginTransformParameters", "_pluginTransformPrivateMethods", "_pluginTransformPrivatePropertyInObject", "_pluginTransformPropertyLiterals", "_pluginTransformRegenerator", "_pluginTransformRegexpModifiers", "_pluginTransformReservedWords", "_pluginTransformShorthandProperties", "_pluginTransformSpread", "_pluginTransformStickyRegex", "_pluginTransformTemplateLiterals", "_pluginTransformTypeofSymbol", "_pluginTransformUnicodeEscapes", "_pluginTransformUnicodePropertyRegex", "_pluginTransformUnicodeRegex", "_pluginTransformUnicodeSetsRegex", "_index", "_index2", "_index3", "_pluginBugfixFirefoxClassInComputedClassKey", "_index4", "_index5", "_index6", "_pluginBugfixSafariIdDestructuringCollisionInFunctionExpression", "_pluginBugfixSafariClassFieldInitializerScope", "_pluginBugfixV8SpreadParametersInOptionalChaining", "_pluginBugfixV8StaticClassFieldsRedefineReadonly", "availablePlugins", "exports", "default", "bugfix/transform-async-arrows-in-class", "bugfixAsyncArrowsInClass", "bugfix/transform-edge-default-parameters", "bugfixEdgeDefaultParameters", "bugfix/transform-edge-function-name", "bugfixEdgeFunctionName", "bugfix/transform-firefox-class-in-computed-class-key", "bugfixFirefoxClassInComputedKey", "bugfix/transform-safari-block-shadowing", "bugfixSafariBlockShadowing", "bugfix/transform-safari-class-field-initializer-scope", "bugfixSafariClassFieldInitializerScope", "bugfix/transform-safari-for-shadowing", "bugfixSafariForShadowing", "bugfix/transform-safari-id-destructuring-collision-in-function-expression", "bugfixSafariIdDestructuringCollisionInFunctionExpression", "bugfix/transform-tagged-template-caching", "bugfixTaggedTemplateCaching", "bugfix/transform-v8-spread-parameters-in-optional-chaining", "bugfixV8SpreadParametersInOptionalChaining", "bugfix/transform-v8-static-class-fields-redefine-readonly", "bugfixV8StaticClassFieldsRedefineReadonly", "transform-arrow-functions", "transformArrowFunctions", "transform-async-generator-functions", "transformAsyncGeneratorFunctions", "transform-async-to-generator", "transformAsyncToGenerator", "transform-block-scoped-functions", "transformBlockScopedFunctions", "transform-block-scoping", "transformBlockScoping", "transform-class-properties", "transformClassProperties", "transform-class-static-block", "transformClassStaticBlock", "transform-classes", "transformClasses", "transform-computed-properties", "transformComputedProperties", "transform-destructuring", "transformDestructuring", "transform-dotall-regex", "transformDotallRegex", "transform-duplicate-keys", "transformDuplicateKeys", "transform-duplicate-named-capturing-groups-regex", "transformDuplicateNamedCapturingGroupsRegex", "transform-dynamic-import", "transformDynamicImport", "transform-exponentiation-operator", "transformExponentialOperator", "transform-export-namespace-from", "transformExportNamespaceFrom", "transform-for-of", "transformForOf", "transform-function-name", "transformFunctionName", "transform-json-strings", "transformJsonStrings", "transform-literals", "transformLiterals", "transform-logical-assignment-operators", "transformLogicalAssignmentOperators", "transform-member-expression-literals", "transformMemberExpressionLiterals", "transform-modules-amd", "transformModulesAmd", "transform-modules-commonjs", "transformModulesCommonjs", "transform-modules-systemjs", "transformModulesSystemjs", "transform-modules-umd", "transformModulesUmd", "transform-named-capturing-groups-regex", "transformNamedCapturingGroupsRegex", "transform-new-target", "transformNewTarget", "transform-nullish-coalescing-operator", "transformNullishCoalescingOperator", "transform-numeric-separator", "transformNumericSeparator", "transform-object-rest-spread", "transformObjectRestSpread", "transform-object-super", "transformObjectSuper", "transform-optional-catch-binding", "transformOptionalCatchBinding", "transform-optional-chaining", "transformOptionalChaining", "transform-parameters", "transformParameters", "transform-private-methods", "transformPrivateMethods", "transform-private-property-in-object", "transformPrivatePropertyInObject", "transform-property-literals", "transformPropertyLiterals", "transform-regenerator", "transformRegenerator", "transform-regexp-modifiers", "transformRegExpModifiers", "transform-reserved-words", "transformReservedWords", "transform-shorthand-properties", "transformShorthandProperties", "transform-spread", "transformSpread", "transform-sticky-regex", "transformStickyRegex", "transform-template-literals", "transformTemplateLiterals", "transform-typeof-symbol", "transformTypeofSymbol", "transform-unicode-escapes", "transformUnicodeEscapes", "transform-unicode-property-regex", "transformUnicodePropertyRegex", "transform-unicode-regex", "transformUnicodeRegex", "transform-unicode-sets-regex", "transformUnicodeSetsRegex", "minVersions", "legacyBabel7SyntaxPlugins", "Object", "assign", "syntax", "name", "manipulateOptions", "_", "p", "plugins", "push", "legacyBabel7SyntaxPluginsLoaders", "syntax-import-assertions", "syntaxImportAssertions", "syntax-import-attributes", "syntaxImportAttributes", "syntax-unicode-sets-regex", "Set", "keys"], "sources": ["../src/available-plugins.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\n\nimport syntaxImportAssertions from \"@babel/plugin-syntax-import-assertions\" with { if: \"!process.env.BABEL_8_BREAKING\" };\nimport syntaxImportAttributes from \"@babel/plugin-syntax-import-attributes\" with { if: \"!process.env.BABEL_8_BREAKING\" };\n\nimport transformAsyncGeneratorFunctions from \"@babel/plugin-transform-async-generator-functions\";\nimport transformAsyncToGenerator from \"@babel/plugin-transform-async-to-generator\";\nimport transformArrowFunctions from \"@babel/plugin-transform-arrow-functions\";\nimport transformBlockScopedFunctions from \"@babel/plugin-transform-block-scoped-functions\";\nimport transformBlockScoping from \"@babel/plugin-transform-block-scoping\";\nimport transformClasses from \"@babel/plugin-transform-classes\";\nimport transformClassProperties from \"@babel/plugin-transform-class-properties\";\nimport transformClassStaticBlock from \"@babel/plugin-transform-class-static-block\";\nimport transformComputedProperties from \"@babel/plugin-transform-computed-properties\";\nimport transformDestructuring from \"@babel/plugin-transform-destructuring\";\nimport transformDotallRegex from \"@babel/plugin-transform-dotall-regex\";\nimport transformDuplicateKeys from \"@babel/plugin-transform-duplicate-keys\";\nimport transformDuplicateNamedCapturingGroupsRegex from \"@babel/plugin-transform-duplicate-named-capturing-groups-regex\";\nimport transformDynamicImport from \"@babel/plugin-transform-dynamic-import\";\nimport transformExponentialOperator from \"@babel/plugin-transform-exponentiation-operator\";\nimport transformExportNamespaceFrom from \"@babel/plugin-transform-export-namespace-from\";\nimport transformForOf from \"@babel/plugin-transform-for-of\";\nimport transformFunctionName from \"@babel/plugin-transform-function-name\";\nimport transformJsonStrings from \"@babel/plugin-transform-json-strings\";\nimport transformLiterals from \"@babel/plugin-transform-literals\";\nimport transformLogicalAssignmentOperators from \"@babel/plugin-transform-logical-assignment-operators\";\nimport transformMemberExpressionLiterals from \"@babel/plugin-transform-member-expression-literals\";\nimport transformModulesAmd from \"@babel/plugin-transform-modules-amd\";\nimport transformModulesCommonjs from \"@babel/plugin-transform-modules-commonjs\";\nimport transformModulesSystemjs from \"@babel/plugin-transform-modules-systemjs\";\nimport transformModulesUmd from \"@babel/plugin-transform-modules-umd\";\nimport transformNamedCapturingGroupsRegex from \"@babel/plugin-transform-named-capturing-groups-regex\";\nimport transformNewTarget from \"@babel/plugin-transform-new-target\";\nimport transformNullishCoalescingOperator from \"@babel/plugin-transform-nullish-coalescing-operator\";\nimport transformNumericSeparator from \"@babel/plugin-transform-numeric-separator\";\nimport transformObjectRestSpread from \"@babel/plugin-transform-object-rest-spread\";\nimport transformObjectSuper from \"@babel/plugin-transform-object-super\";\nimport transformOptionalCatchBinding from \"@babel/plugin-transform-optional-catch-binding\";\nimport transformOptionalChaining from \"@babel/plugin-transform-optional-chaining\";\nimport transformParameters from \"@babel/plugin-transform-parameters\";\nimport transformPrivateMethods from \"@babel/plugin-transform-private-methods\";\nimport transformPrivatePropertyInObject from \"@babel/plugin-transform-private-property-in-object\";\nimport transformPropertyLiterals from \"@babel/plugin-transform-property-literals\";\nimport transformRegenerator from \"@babel/plugin-transform-regenerator\";\nimport transformRegExpModifiers from \"@babel/plugin-transform-regexp-modifiers\";\nimport transformReservedWords from \"@babel/plugin-transform-reserved-words\";\nimport transformShorthandProperties from \"@babel/plugin-transform-shorthand-properties\";\nimport transformSpread from \"@babel/plugin-transform-spread\";\nimport transformStickyRegex from \"@babel/plugin-transform-sticky-regex\";\nimport transformTemplateLiterals from \"@babel/plugin-transform-template-literals\";\nimport transformTypeofSymbol from \"@babel/plugin-transform-typeof-symbol\";\nimport transformUnicodeEscapes from \"@babel/plugin-transform-unicode-escapes\";\nimport transformUnicodePropertyRegex from \"@babel/plugin-transform-unicode-property-regex\";\nimport transformUnicodeRegex from \"@babel/plugin-transform-unicode-regex\";\nimport transformUnicodeSetsRegex from \"@babel/plugin-transform-unicode-sets-regex\";\n\nimport bugfixAsyncArrowsInClass from \"@babel/preset-modules/lib/plugins/transform-async-arrows-in-class/index.js\";\nimport bugfixEdgeDefaultParameters from \"@babel/preset-modules/lib/plugins/transform-edge-default-parameters/index.js\";\nimport bugfixEdgeFunctionName from \"@babel/preset-modules/lib/plugins/transform-edge-function-name/index.js\";\nimport bugfixFirefoxClassInComputedKey from \"@babel/plugin-bugfix-firefox-class-in-computed-class-key\";\nimport bugfixTaggedTemplateCaching from \"@babel/preset-modules/lib/plugins/transform-tagged-template-caching/index.js\";\nimport bugfixSafariBlockShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-block-shadowing/index.js\";\nimport bugfixSafariForShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-for-shadowing/index.js\";\nimport bugfixSafariIdDestructuringCollisionInFunctionExpression from \"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression\";\nimport bugfixSafariClassFieldInitializerScope from \"@babel/plugin-bugfix-safari-class-field-initializer-scope\";\nimport bugfixV8SpreadParametersInOptionalChaining from \"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining\";\nimport bugfixV8StaticClassFieldsRedefineReadonly from \"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly\";\n\nexport { availablePlugins as default };\nconst availablePlugins = {\n  \"bugfix/transform-async-arrows-in-class\": () => bugfixAsyncArrowsInClass,\n  \"bugfix/transform-edge-default-parameters\": () => bugfixEdgeDefaultParameters,\n  \"bugfix/transform-edge-function-name\": () => bugfixEdgeFunctionName,\n  \"bugfix/transform-firefox-class-in-computed-class-key\": () =>\n    bugfixFirefoxClassInComputedKey,\n  \"bugfix/transform-safari-block-shadowing\": () => bugfixSafariBlockShadowing,\n  \"bugfix/transform-safari-class-field-initializer-scope\": () =>\n    bugfixSafariClassFieldInitializerScope,\n  \"bugfix/transform-safari-for-shadowing\": () => bugfixSafariForShadowing,\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    () => bugfixSafariIdDestructuringCollisionInFunctionExpression,\n  \"bugfix/transform-tagged-template-caching\": () => bugfixTaggedTemplateCaching,\n  \"bugfix/transform-v8-spread-parameters-in-optional-chaining\": () =>\n    bugfixV8SpreadParametersInOptionalChaining,\n  \"bugfix/transform-v8-static-class-fields-redefine-readonly\": () =>\n    bugfixV8StaticClassFieldsRedefineReadonly,\n  \"transform-arrow-functions\": () => transformArrowFunctions,\n  \"transform-async-generator-functions\": () => transformAsyncGeneratorFunctions,\n  \"transform-async-to-generator\": () => transformAsyncToGenerator,\n  \"transform-block-scoped-functions\": () => transformBlockScopedFunctions,\n  \"transform-block-scoping\": () => transformBlockScoping,\n  \"transform-class-properties\": () => transformClassProperties,\n  \"transform-class-static-block\": () => transformClassStaticBlock,\n  \"transform-classes\": () => transformClasses,\n  \"transform-computed-properties\": () => transformComputedProperties,\n  \"transform-destructuring\": () => transformDestructuring,\n  \"transform-dotall-regex\": () => transformDotallRegex,\n  \"transform-duplicate-keys\": () => transformDuplicateKeys,\n  \"transform-duplicate-named-capturing-groups-regex\": () =>\n    transformDuplicateNamedCapturingGroupsRegex,\n  \"transform-dynamic-import\": () => transformDynamicImport,\n  \"transform-exponentiation-operator\": () => transformExponentialOperator,\n  \"transform-export-namespace-from\": () => transformExportNamespaceFrom,\n  \"transform-for-of\": () => transformForOf,\n  \"transform-function-name\": () => transformFunctionName,\n  \"transform-json-strings\": () => transformJsonStrings,\n  \"transform-literals\": () => transformLiterals,\n  \"transform-logical-assignment-operators\": () =>\n    transformLogicalAssignmentOperators,\n  \"transform-member-expression-literals\": () =>\n    transformMemberExpressionLiterals,\n  \"transform-modules-amd\": () => transformModulesAmd,\n  \"transform-modules-commonjs\": () => transformModulesCommonjs,\n  \"transform-modules-systemjs\": () => transformModulesSystemjs,\n  \"transform-modules-umd\": () => transformModulesUmd,\n  \"transform-named-capturing-groups-regex\": () =>\n    transformNamedCapturingGroupsRegex,\n  \"transform-new-target\": () => transformNewTarget,\n  \"transform-nullish-coalescing-operator\": () =>\n    transformNullishCoalescingOperator,\n  \"transform-numeric-separator\": () => transformNumericSeparator,\n  \"transform-object-rest-spread\": () => transformObjectRestSpread,\n  \"transform-object-super\": () => transformObjectSuper,\n  \"transform-optional-catch-binding\": () => transformOptionalCatchBinding,\n  \"transform-optional-chaining\": () => transformOptionalChaining,\n  \"transform-parameters\": () => transformParameters,\n  \"transform-private-methods\": () => transformPrivateMethods,\n  \"transform-private-property-in-object\": () =>\n    transformPrivatePropertyInObject,\n  \"transform-property-literals\": () => transformPropertyLiterals,\n  \"transform-regenerator\": () => transformRegenerator,\n  \"transform-regexp-modifiers\": () => transformRegExpModifiers,\n  \"transform-reserved-words\": () => transformReservedWords,\n  \"transform-shorthand-properties\": () => transformShorthandProperties,\n  \"transform-spread\": () => transformSpread,\n  \"transform-sticky-regex\": () => transformStickyRegex,\n  \"transform-template-literals\": () => transformTemplateLiterals,\n  \"transform-typeof-symbol\": () => transformTypeofSymbol,\n  \"transform-unicode-escapes\": () => transformUnicodeEscapes,\n  \"transform-unicode-property-regex\": () => transformUnicodePropertyRegex,\n  \"transform-unicode-regex\": () => transformUnicodeRegex,\n  \"transform-unicode-sets-regex\": () => transformUnicodeSetsRegex,\n};\n\nexport const minVersions = {};\n// TODO(Babel 8): Remove this\nexport let legacyBabel7SyntaxPlugins: Set<string>;\n\nif (!process.env.BABEL_8_BREAKING) {\n  /* eslint-disable no-restricted-globals */\n\n  Object.assign(minVersions, {\n    \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n      \"7.16.0\",\n    \"bugfix/transform-v8-static-class-fields-redefine-readonly\": \"7.12.0\",\n    \"syntax-import-attributes\": \"7.22.0\",\n    \"transform-class-static-block\": \"7.12.0\",\n    \"transform-duplicate-named-capturing-groups-regex\": \"7.19.0\",\n    \"transform-private-property-in-object\": \"7.10.0\",\n    \"transform-regexp-modifiers\": \"7.19.0\",\n  });\n\n  // This is a factory to create a plugin that enables a parser plugin\n  const syntax =\n    (name: ParserPlugin) => (): typeof transformJsonStrings => () => ({\n      manipulateOptions: (_, p) => p.plugins.push(name),\n    });\n  type ParserPlugin = Parameters<\n    ReturnType<typeof transformJsonStrings>[\"manipulateOptions\"]\n  >[1][\"plugins\"][number];\n\n  const legacyBabel7SyntaxPluginsLoaders = {\n    \"syntax-async-generators\": syntax(\"asyncGenerators\"),\n    \"syntax-class-properties\": syntax(\"classProperties\"),\n    \"syntax-class-static-block\": syntax(\"classStaticBlock\"),\n    \"syntax-dynamic-import\": syntax(\"dynamicImport\"),\n    \"syntax-export-namespace-from\": syntax(\"exportNamespaceFrom\"),\n    \"syntax-import-meta\": syntax(\"importMeta\"),\n    \"syntax-json-strings\": syntax(\"jsonStrings\"),\n    \"syntax-logical-assignment-operators\": syntax(\"logicalAssignment\"),\n    \"syntax-nullish-coalescing-operator\": syntax(\"nullishCoalescingOperator\"),\n    \"syntax-numeric-separator\": syntax(\"numericSeparator\"),\n    \"syntax-object-rest-spread\": syntax(\"objectRestSpread\"),\n    \"syntax-optional-catch-binding\": syntax(\"optionalCatchBinding\"),\n    \"syntax-optional-chaining\": syntax(\"optionalChaining\"),\n    \"syntax-private-property-in-object\": syntax(\"privateIn\"),\n    \"syntax-top-level-await\": syntax(\"topLevelAwait\"),\n\n    // These plugins have more logic than just enabling/disabling a feature\n    // eslint-disable-next-line sort-keys\n    \"syntax-import-assertions\": () => syntaxImportAssertions,\n    \"syntax-import-attributes\": () => syntaxImportAttributes,\n\n    // These are CJS plugins that depend on a package from the monorepo, so it\n    // breaks using ESM. Given that ESM builds are new enough to have this\n    // syntax enabled by default, we can safely skip enabling it.\n\n    \"syntax-unicode-sets-regex\":\n      USE_ESM || IS_STANDALONE\n        ? () => () => ({})\n        : () => require(\"@babel/plugin-syntax-unicode-sets-regex\"),\n  };\n\n  Object.assign(availablePlugins, legacyBabel7SyntaxPluginsLoaders);\n\n  legacyBabel7SyntaxPlugins = new Set(\n    Object.keys(legacyBabel7SyntaxPluginsLoaders),\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,6BAAA,GAAAC,OAAA;AACA,IAAAC,6BAAA,GAAAD,OAAA;AAEA,IAAAE,uCAAA,GAAAF,OAAA;AACA,IAAAG,gCAAA,GAAAH,OAAA;AACA,IAAAI,8BAAA,GAAAJ,OAAA;AACA,IAAAK,oCAAA,GAAAL,OAAA;AACA,IAAAM,4BAAA,GAAAN,OAAA;AACA,IAAAO,uBAAA,GAAAP,OAAA;AACA,IAAAQ,+BAAA,GAAAR,OAAA;AACA,IAAAS,gCAAA,GAAAT,OAAA;AACA,IAAAU,kCAAA,GAAAV,OAAA;AACA,IAAAW,6BAAA,GAAAX,OAAA;AACA,IAAAY,2BAAA,GAAAZ,OAAA;AACA,IAAAa,6BAAA,GAAAb,OAAA;AACA,IAAAc,kDAAA,GAAAd,OAAA;AACA,IAAAe,6BAAA,GAAAf,OAAA;AACA,IAAAgB,sCAAA,GAAAhB,OAAA;AACA,IAAAiB,mCAAA,GAAAjB,OAAA;AACA,IAAAkB,qBAAA,GAAAlB,OAAA;AACA,IAAAmB,4BAAA,GAAAnB,OAAA;AACA,IAAAoB,2BAAA,GAAApB,OAAA;AACA,IAAAqB,wBAAA,GAAArB,OAAA;AACA,IAAAsB,0CAAA,GAAAtB,OAAA;AACA,IAAAuB,wCAAA,GAAAvB,OAAA;AACA,IAAAwB,0BAAA,GAAAxB,OAAA;AACA,IAAAyB,+BAAA,GAAAzB,OAAA;AACA,IAAA0B,+BAAA,GAAA1B,OAAA;AACA,IAAA2B,0BAAA,GAAA3B,OAAA;AACA,IAAA4B,yCAAA,GAAA5B,OAAA;AACA,IAAA6B,yBAAA,GAAA7B,OAAA;AACA,IAAA8B,yCAAA,GAAA9B,OAAA;AACA,IAAA+B,gCAAA,GAAA/B,OAAA;AACA,IAAAgC,gCAAA,GAAAhC,OAAA;AACA,IAAAiC,2BAAA,GAAAjC,OAAA;AACA,IAAAkC,oCAAA,GAAAlC,OAAA;AACA,IAAAmC,gCAAA,GAAAnC,OAAA;AACA,IAAAoC,0BAAA,GAAApC,OAAA;AACA,IAAAqC,8BAAA,GAAArC,OAAA;AACA,IAAAsC,uCAAA,GAAAtC,OAAA;AACA,IAAAuC,gCAAA,GAAAvC,OAAA;AACA,IAAAwC,2BAAA,GAAAxC,OAAA;AACA,IAAAyC,+BAAA,GAAAzC,OAAA;AACA,IAAA0C,6BAAA,GAAA1C,OAAA;AACA,IAAA2C,mCAAA,GAAA3C,OAAA;AACA,IAAA4C,sBAAA,GAAA5C,OAAA;AACA,IAAA6C,2BAAA,GAAA7C,OAAA;AACA,IAAA8C,gCAAA,GAAA9C,OAAA;AACA,IAAA+C,4BAAA,GAAA/C,OAAA;AACA,IAAAgD,8BAAA,GAAAhD,OAAA;AACA,IAAAiD,oCAAA,GAAAjD,OAAA;AACA,IAAAkD,4BAAA,GAAAlD,OAAA;AACA,IAAAmD,gCAAA,GAAAnD,OAAA;AAEA,IAAAoD,MAAA,GAAApD,OAAA;AACA,IAAAqD,OAAA,GAAArD,OAAA;AACA,IAAAsD,OAAA,GAAAtD,OAAA;AACA,IAAAuD,2CAAA,GAAAvD,OAAA;AACA,IAAAwD,OAAA,GAAAxD,OAAA;AACA,IAAAyD,OAAA,GAAAzD,OAAA;AACA,IAAA0D,OAAA,GAAA1D,OAAA;AACA,IAAA2D,+DAAA,GAAA3D,OAAA;AACA,IAAA4D,6CAAA,GAAA5D,OAAA;AACA,IAAA6D,iDAAA,GAAA7D,OAAA;AACA,IAAA8D,gDAAA,GAAA9D,OAAA;AAGA,MAAM+D,gBAAgB,GAAAC,OAAA,CAAAC,OAAA,GAAG;EACvB,wCAAwC,EAAEC,CAAA,KAAMC,MAAwB;EACxE,0CAA0C,EAAEC,CAAA,KAAMC,OAA2B;EAC7E,qCAAqC,EAAEC,CAAA,KAAMC,OAAsB;EACnE,sDAAsD,EAAEC,CAAA,KACtDC,mDAA+B;EACjC,yCAAyC,EAAEC,CAAA,KAAMC,OAA0B;EAC3E,uDAAuD,EAAEC,CAAA,KACvDC,qDAAsC;EACxC,uCAAuC,EAAEC,CAAA,KAAMC,OAAwB;EACvE,2EAA2E,EACzEC,CAAA,KAAMC,uEAAwD;EAChE,0CAA0C,EAAEC,CAAA,KAAMC,OAA2B;EAC7E,4DAA4D,EAAEC,CAAA,KAC5DC,yDAA0C;EAC5C,2DAA2D,EAAEC,CAAA,KAC3DC,wDAAyC;EAC3C,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,qCAAqC,EAAEC,CAAA,KAAMC,+CAAgC;EAC7E,8BAA8B,EAAEC,CAAA,KAAMC,wCAAyB;EAC/D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA6B;EACvE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAyB;EAC/D,mBAAmB,EAAEC,CAAA,KAAMC,+BAAgB;EAC3C,+BAA+B,EAAEC,CAAA,KAAMC,0CAA2B;EAClE,yBAAyB,EAAEC,CAAA,KAAMC,qCAAsB;EACvD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,kDAAkD,EAAEC,CAAA,KAClDC,0DAA2C;EAC7C,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,mCAAmC,EAAEC,CAAA,KAAMC,8CAA4B;EACvE,iCAAiC,EAAEC,CAAA,KAAMC,2CAA4B;EACrE,kBAAkB,EAAEC,CAAA,KAAMC,6BAAc;EACxC,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,oBAAoB,EAAEC,CAAA,KAAMC,gCAAiB;EAC7C,wCAAwC,EAAEC,CAAA,KACxCC,kDAAmC;EACrC,sCAAsC,EAAEC,CAAA,KACtCC,gDAAiC;EACnC,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,uBAAuB,EAAEC,CAAA,KAAMC,kCAAmB;EAClD,wCAAwC,EAAEC,CAAA,KACxCC,iDAAkC;EACpC,sBAAsB,EAAEC,CAAA,KAAMC,iCAAkB;EAChD,uCAAuC,EAAEC,CAAA,KACvCC,iDAAkC;EACpC,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,8BAA8B,EAAEC,CAAA,KAAMC,wCAAyB;EAC/D,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,kCAAkC,EAAEC,CAAA,KAAMC,4CAA6B;EACvE,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,sBAAsB,EAAEC,CAAA,KAAMC,kCAAmB;EACjD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,sCAAsC,EAAEC,CAAA,KACtCC,+CAAgC;EAClC,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,uBAAuB,EAAEC,CAAA,KAAMC,mCAAoB;EACnD,4BAA4B,EAAEC,CAAA,KAAMC,uCAAwB;EAC5D,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;EACxD,gCAAgC,EAAEC,CAAA,KAAMC,2CAA4B;EACpE,kBAAkB,EAAEC,CAAA,KAAMC,8BAAe;EACzC,wBAAwB,EAAEC,CAAA,KAAMC,mCAAoB;EACpD,6BAA6B,EAAEC,CAAA,KAAMC,wCAAyB;EAC9D,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,2BAA2B,EAAEC,CAAA,KAAMC,sCAAuB;EAC1D,kCAAkC,EAAEC,CAAA,KAAMC,4CAA6B;EACvE,yBAAyB,EAAEC,CAAA,KAAMC,oCAAqB;EACtD,8BAA8B,EAAEC,CAAA,KAAMC;AACxC,CAAC;AAEM,MAAMC,WAAW,GAAA5H,OAAA,CAAA4H,WAAA,GAAG,CAAC,CAAC;AAEtB,IAAIC,yBAAsC,GAAA7H,OAAA,CAAA6H,yBAAA;AAEd;EAGjCC,MAAM,CAACC,MAAM,CAACH,WAAW,EAAE;IACzB,2EAA2E,EACzE,QAAQ;IACV,2DAA2D,EAAE,QAAQ;IACrE,0BAA0B,EAAE,QAAQ;IACpC,8BAA8B,EAAE,QAAQ;IACxC,kDAAkD,EAAE,QAAQ;IAC5D,sCAAsC,EAAE,QAAQ;IAChD,4BAA4B,EAAE;EAChC,CAAC,CAAC;EAGF,MAAMI,MAAM,GACTC,IAAkB,IAAK,MAAmC,OAAO;IAChEC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,OAAO,CAACC,IAAI,CAACL,IAAI;EAClD,CAAC,CAAC;EAKJ,MAAMM,gCAAgC,GAAG;IACvC,yBAAyB,EAAEP,MAAM,CAAC,iBAAiB,CAAC;IACpD,yBAAyB,EAAEA,MAAM,CAAC,iBAAiB,CAAC;IACpD,2BAA2B,EAAEA,MAAM,CAAC,kBAAkB,CAAC;IACvD,uBAAuB,EAAEA,MAAM,CAAC,eAAe,CAAC;IAChD,8BAA8B,EAAEA,MAAM,CAAC,qBAAqB,CAAC;IAC7D,oBAAoB,EAAEA,MAAM,CAAC,YAAY,CAAC;IAC1C,qBAAqB,EAAEA,MAAM,CAAC,aAAa,CAAC;IAC5C,qCAAqC,EAAEA,MAAM,CAAC,mBAAmB,CAAC;IAClE,oCAAoC,EAAEA,MAAM,CAAC,2BAA2B,CAAC;IACzE,0BAA0B,EAAEA,MAAM,CAAC,kBAAkB,CAAC;IACtD,2BAA2B,EAAEA,MAAM,CAAC,kBAAkB,CAAC;IACvD,+BAA+B,EAAEA,MAAM,CAAC,sBAAsB,CAAC;IAC/D,0BAA0B,EAAEA,MAAM,CAAC,kBAAkB,CAAC;IACtD,mCAAmC,EAAEA,MAAM,CAAC,WAAW,CAAC;IACxD,wBAAwB,EAAEA,MAAM,CAAC,eAAe,CAAC;IAIjD,0BAA0B,EAAEQ,CAAA,KAAMC,qCAAsB;IACxD,0BAA0B,EAAEC,CAAA,KAAMC,qCAAsB;IAMxD,2BAA2B,EAGrBC,CAAA,KAAM5M,OAAO,CAAC,yCAAyC;EAC/D,CAAC;EAED8L,MAAM,CAACC,MAAM,CAAChI,gBAAgB,EAAEwI,gCAAgC,CAAC;EAEjEvI,OAAA,CAAA6H,yBAAA,GAAAA,yBAAyB,GAAG,IAAIgB,GAAG,CACjCf,MAAM,CAACgB,IAAI,CAACP,gCAAgC,CAC9C,CAAC;AACH", "ignoreList": []}