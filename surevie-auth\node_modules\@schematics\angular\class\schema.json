{"$schema": "http://json-schema.org/draft-07/schema", "$id": "SchematicsAngularClass", "title": "Angular Class Options Schema", "type": "object", "description": "Creates a new, generic class definition in the given project.", "additionalProperties": false, "properties": {"name": {"type": "string", "description": "The name of the new class.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the class?"}, "path": {"type": "string", "format": "path", "$default": {"$source": "workingDirectory"}, "description": "The path at which to create the class, relative to the workspace root.", "visible": false}, "project": {"type": "string", "description": "The name of the project.", "$default": {"$source": "projectName"}}, "skipTests": {"type": "boolean", "description": "Do not create \"spec.ts\" test files for the new class.", "default": false}, "type": {"type": "string", "description": "Adds a developer-defined type to the filename, in the format \"name.type.ts\"."}}, "required": ["name", "project"]}