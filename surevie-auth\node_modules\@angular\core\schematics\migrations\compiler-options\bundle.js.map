{"version": 3, "sources": ["../../../../../../../../packages/core/schematics/migrations/compiler-options/index.ts", "../../../../../../../../packages/core/schematics/utils/project_tsconfig_paths.ts", "../../../../../../../../packages/core/schematics/utils/typescript/compiler_host.ts", "../../../../../../../../packages/core/schematics/utils/typescript/parse_tsconfig.ts", "../../../../../../../../packages/core/schematics/migrations/compiler-options/util.ts", "../../../../../../../../packages/core/schematics/utils/change_tracker.ts", "../../../../../../../../packages/core/schematics/utils/import_manager.ts", "../../../../../../../../packages/core/schematics/utils/typescript/imports.ts", "../../../../../../../../packages/core/schematics/utils/typescript/nodes.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Rule, SchematicsException, Tree, UpdateRecorder} from '@angular-devkit/schematics';\nimport {relative} from 'path';\n\nimport {getProjectTsConfigPaths} from '../../utils/project_tsconfig_paths';\nimport {canMigrateFile, createMigrationProgram} from '../../utils/typescript/compiler_host';\n\nimport {migrateFile} from './util';\n\nexport default function(): Rule {\n  return async (tree: Tree) => {\n    const {buildPaths, testPaths} = await getProjectTsConfigPaths(tree);\n    const basePath = process.cwd();\n    const allPaths = [...buildPaths, ...testPaths];\n\n    if (!allPaths.length) {\n      throw new SchematicsException(\n          'Could not find any tsconfig file. Cannot run the guard and resolve interfaces migration.');\n    }\n\n    for (const tsconfigPath of allPaths) {\n      runGuardAndResolveInterfacesMigration(tree, tsconfigPath, basePath);\n    }\n  };\n}\n\nfunction runGuardAndResolveInterfacesMigration(tree: Tree, tsconfigPath: string, basePath: string) {\n  const program = createMigrationProgram(tree, tsconfigPath, basePath);\n  const sourceFiles = program.getSourceFiles().filter(\n      (sourceFile) => canMigrateFile(basePath, sourceFile, program));\n\n  for (const sourceFile of sourceFiles) {\n    let update: UpdateRecorder|null = null;\n\n    const rewriter = (startPos: number, width: number, text: string|null) => {\n      if (update === null) {\n        // Lazily initialize update, because most files will not require migration.\n        update = tree.beginUpdate(relative(basePath, sourceFile.fileName));\n      }\n      update.remove(startPos, width);\n      if (text !== null) {\n        update.insertLeft(startPos, text);\n      }\n    };\n    migrateFile(sourceFile, rewriter);\n\n    if (update !== null) {\n      tree.commitUpdate(update);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {json, normalize, virtualFs, workspaces} from '@angular-devkit/core';\nimport {Tree} from '@angular-devkit/schematics';\n\n/**\n * Gets all tsconfig paths from a CLI project by reading the workspace configuration\n * and looking for common tsconfig locations.\n */\nexport async function getProjectTsConfigPaths(tree: Tree):\n    Promise<{buildPaths: string[]; testPaths: string[];}> {\n  // Start with some tsconfig paths that are generally used within CLI projects. Note\n  // that we are not interested in IDE-specific tsconfig files (e.g. /tsconfig.json)\n  const buildPaths = new Set<string>();\n  const testPaths = new Set<string>();\n\n  const workspace = await getWorkspace(tree);\n  for (const [, project] of workspace.projects) {\n    for (const [name, target] of project.targets) {\n      if (name !== 'build' && name !== 'test') {\n        continue;\n      }\n\n      for (const [, options] of allTargetOptions(target)) {\n        const tsConfig = options['tsConfig'];\n        // Filter out tsconfig files that don't exist in the CLI project.\n        if (typeof tsConfig !== 'string' || !tree.exists(tsConfig)) {\n          continue;\n        }\n\n        if (name === 'build') {\n          buildPaths.add(normalize(tsConfig));\n        } else {\n          testPaths.add(normalize(tsConfig));\n        }\n      }\n    }\n  }\n\n  return {\n    buildPaths: [...buildPaths],\n    testPaths: [...testPaths],\n  };\n}\n\n/** Get options for all configurations for the passed builder target. */\nfunction*\n    allTargetOptions(target: workspaces.TargetDefinition):\n        Iterable<[string | undefined, Record<string, json.JsonValue|undefined>]> {\n  if (target.options) {\n    yield [undefined, target.options];\n  }\n\n  if (!target.configurations) {\n    return;\n  }\n\n  for (const [name, options] of Object.entries(target.configurations)) {\n    if (options) {\n      yield [name, options];\n    }\n  }\n}\n\nfunction createHost(tree: Tree): workspaces.WorkspaceHost {\n  return {\n    async readFile(path: string): Promise<string> {\n      const data = tree.read(path);\n      if (!data) {\n        throw new Error('File not found.');\n      }\n\n      return virtualFs.fileBufferToString(data);\n    },\n    async writeFile(path: string, data: string): Promise<void> {\n      return tree.overwrite(path, data);\n    },\n    async isDirectory(path: string): Promise<boolean> {\n      // Approximate a directory check.\n      // We don't need to consider empty directories and hence this is a good enough approach.\n      // This is also per documentation, see:\n      // https://angular.io/guide/schematics-for-libraries#get-the-project-configuration\n      return !tree.exists(path) && tree.getDir(path).subfiles.length > 0;\n    },\n    async isFile(path: string): Promise<boolean> {\n      return tree.exists(path);\n    },\n  };\n}\n\nasync function getWorkspace(tree: Tree): Promise<workspaces.WorkspaceDefinition> {\n  const host = createHost(tree);\n  const {workspace} = await workspaces.readWorkspace('/', host);\n\n  return workspace;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {Tree} from '@angular-devkit/schematics';\nimport {dirname, relative, resolve} from 'path';\nimport ts from 'typescript';\n\nimport {parseTsconfigFile} from './parse_tsconfig';\n\ntype FakeReadFileFn = (fileName: string) => string|undefined;\n\n/**\n * Creates a TypeScript program instance for a TypeScript project within\n * the virtual file system tree.\n * @param tree Virtual file system tree that contains the source files.\n * @param tsconfigPath Virtual file system path that resolves to the TypeScript project.\n * @param basePath Base path for the virtual file system tree.\n * @param fakeFileRead Optional file reader function. Can be used to overwrite files in\n *   the TypeScript program, or to add in-memory files (e.g. to add global types).\n * @param additionalFiles Additional file paths that should be added to the program.\n */\nexport function createMigrationProgram(\n    tree: Tree, tsconfigPath: string, basePath: string, fakeFileRead?: FakeReadFileFn,\n    additionalFiles?: string[]) {\n  const {rootNames, options, host} =\n      createProgramOptions(tree, tsconfigPath, basePath, fakeFileRead, additionalFiles);\n  return ts.createProgram(rootNames, options, host);\n}\n\n/**\n * Creates the options necessary to instantiate a TypeScript program.\n * @param tree Virtual file system tree that contains the source files.\n * @param tsconfigPath Virtual file system path that resolves to the TypeScript project.\n * @param basePath Base path for the virtual file system tree.\n * @param fakeFileRead Optional file reader function. Can be used to overwrite files in\n *   the TypeScript program, or to add in-memory files (e.g. to add global types).\n * @param additionalFiles Additional file paths that should be added to the program.\n * @param optionOverrides Overrides of the parsed compiler options.\n */\nexport function createProgramOptions(\n    tree: Tree, tsconfigPath: string, basePath: string, fakeFileRead?: FakeReadFileFn,\n    additionalFiles?: string[], optionOverrides?: ts.CompilerOptions) {\n  // Resolve the tsconfig path to an absolute path. This is needed as TypeScript otherwise\n  // is not able to resolve root directories in the given tsconfig. More details can be found\n  // in the following issue: https://github.com/microsoft/TypeScript/issues/37731.\n  tsconfigPath = resolve(basePath, tsconfigPath);\n  const parsed = parseTsconfigFile(tsconfigPath, dirname(tsconfigPath));\n  const options = optionOverrides ? {...parsed.options, ...optionOverrides} : parsed.options;\n  const host = createMigrationCompilerHost(tree, options, basePath, fakeFileRead);\n  return {rootNames: parsed.fileNames.concat(additionalFiles || []), options, host};\n}\n\nfunction createMigrationCompilerHost(\n    tree: Tree, options: ts.CompilerOptions, basePath: string,\n    fakeRead?: FakeReadFileFn): ts.CompilerHost {\n  const host = ts.createCompilerHost(options, true);\n  const defaultReadFile = host.readFile;\n\n  // We need to overwrite the host \"readFile\" method, as we want the TypeScript\n  // program to be based on the file contents in the virtual file tree. Otherwise\n  // if we run multiple migrations we might have intersecting changes and\n  // source files.\n  host.readFile = fileName => {\n    const treeRelativePath = relative(basePath, fileName);\n    let result: string|undefined = fakeRead?.(treeRelativePath);\n\n    if (typeof result !== 'string') {\n      // If the relative path resolved to somewhere outside of the tree, fall back to\n      // TypeScript's default file reading function since the `tree` will throw an error.\n      result = treeRelativePath.startsWith('..') ? defaultReadFile.call(host, fileName) :\n                                                   tree.read(treeRelativePath)?.toString();\n    }\n\n    // Strip BOM as otherwise TSC methods (Ex: getWidth) will return an offset,\n    // which breaks the CLI UpdateRecorder.\n    // See: https://github.com/angular/angular/pull/30719\n    return typeof result === 'string' ? result.replace(/^\\uFEFF/, '') : undefined;\n  };\n\n  return host;\n}\n\n/**\n * Checks whether a file can be migrate by our automated migrations.\n * @param basePath Absolute path to the project.\n * @param sourceFile File being checked.\n * @param program Program that includes the source file.\n */\nexport function canMigrateFile(\n    basePath: string, sourceFile: ts.SourceFile, program: ts.Program): boolean {\n  // We shouldn't migrate .d.ts files, files from an external library or type checking files.\n  if (sourceFile.fileName.endsWith('.ngtypecheck.ts') || sourceFile.isDeclarationFile ||\n      program.isSourceFileFromExternalLibrary(sourceFile)) {\n    return false;\n  }\n\n  // Our migrations are set up to create a `Program` from the project's tsconfig and to migrate all\n  // the files within the program. This can include files that are outside of the Angular CLI\n  // project. We can't migrate files outside of the project, because our file system interactions\n  // go through the CLI's `Tree` which assumes that all files are within the project. See:\n  // https://github.com/angular/angular-cli/blob/0b0961c9c233a825b6e4bb59ab7f0790f9b14676/packages/angular_devkit/schematics/src/tree/host-tree.ts#L131\n  return !relative(basePath, sourceFile.fileName).startsWith('..');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport * as path from 'path';\nimport ts from 'typescript';\n\nexport function parseTsconfigFile(tsconfigPath: string, basePath: string): ts.ParsedCommandLine {\n  const {config} = ts.readConfigFile(tsconfigPath, ts.sys.readFile);\n  const parseConfigHost = {\n    useCaseSensitiveFileNames: ts.sys.useCaseSensitiveFileNames,\n    fileExists: ts.sys.fileExists,\n    readDirectory: ts.sys.readDirectory,\n    readFile: ts.sys.readFile,\n  };\n\n  // Throw if incorrect arguments are passed to this function. Passing relative base paths\n  // results in root directories not being resolved and in later type checking runtime errors.\n  // More details can be found here: https://github.com/microsoft/TypeScript/issues/37731.\n  if (!path.isAbsolute(basePath)) {\n    throw Error('Unexpected relative base path has been specified.');\n  }\n\n  return ts.parseJsonConfigFileContent(config, parseConfigHost, basePath, {});\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport ts from 'typescript';\n\nimport {ChangeTracker} from '../../utils/change_tracker';\nimport {getImportSpecifier} from '../../utils/typescript/imports';\nimport {closestNode} from '../../utils/typescript/nodes';\n\nconst coreModule = '@angular/core';\nconst compilerOptionsType = 'CompilerOptions';\nconst deletedIdentifiers = new Set(['useJit', 'missingTranslation']);\n\nexport type RewriteFn = (startPos: number, width: number, text: string) => void;\n\nexport function migrateFile(sourceFile: ts.SourceFile, rewriteFn: RewriteFn) {\n  const compilerOptionsImport = getImportSpecifier(sourceFile, coreModule, compilerOptionsType);\n  if (compilerOptionsImport === null) {\n    return;\n  }\n\n  const changeTracker = new ChangeTracker(ts.createPrinter());\n\n  removeIdentifiers(sourceFile, changeTracker);\n\n  for (const changesInFile of changeTracker.recordChanges().values()) {\n    for (const change of changesInFile) {\n      rewriteFn(change.start, change.removeLength ?? 0, change.text);\n    }\n  }\n}\n\nfunction removeIdentifiers(sourceFile: ts.SourceFile, changeTracker: ChangeTracker) {\n  const missingTranslationStrategyImport =\n      getImportSpecifier(sourceFile, coreModule, 'MissingTranslationStrategy');\n  const namedImports = missingTranslationStrategyImport ?\n      closestNode(missingTranslationStrategyImport, ts.isNamedImports) :\n      null;\n\n  const visitNode = (node: ts.Node) => {\n    if (ts.isVariableDeclaration(node) && node.type?.getText() !== compilerOptionsType) {\n      return;\n    }\n\n    if (ts.isIdentifier(node) && deletedIdentifiers.has(node.text)) {\n      changeTracker.removeNode(node.parent);\n      if (node.text === 'missingTranslation') {\n        if (namedImports && missingTranslationStrategyImport) {\n          changeTracker.removeNode(missingTranslationStrategyImport);\n        }\n      }\n    }\n    ts.forEachChild(node, visitNode);\n  };\n  ts.forEachChild(sourceFile, visitNode);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport ts from 'typescript';\n\nimport {ImportManager} from './import_manager';\n\n/** Function that can be used to remap a generated import. */\nexport type ImportRemapper = (moduleName: string, inFile: string) => string;\n\n/** Mapping between a source file and the changes that have to be applied to it. */\nexport type ChangesByFile = ReadonlyMap<ts.SourceFile, PendingChange[]>;\n\n/** Change that needs to be applied to a file. */\nexport interface PendingChange {\n  /** Index at which to start changing the file. */\n  start: number;\n  /**\n   * Amount of text that should be removed after the `start`.\n   * No text will be removed if omitted.\n   */\n  removeLength?: number;\n  /** New text that should be inserted. */\n  text: string;\n}\n\n/** Tracks changes that have to be made for specific files. */\nexport class ChangeTracker {\n  private readonly _changes = new Map<ts.SourceFile, PendingChange[]>();\n  private readonly _importManager: ImportManager;\n\n  constructor(private _printer: ts.Printer, private _importRemapper?: ImportRemapper) {\n    this._importManager = new ImportManager(\n        currentFile => ({\n          addNewImport: (start, text) => this.insertText(currentFile, start, text),\n          updateExistingImport: (namedBindings, text) => this.replaceText(\n              currentFile, namedBindings.getStart(), namedBindings.getWidth(), text),\n        }),\n        this._printer);\n  }\n\n  /**\n   * Tracks the insertion of some text.\n   * @param sourceFile File in which the text is being inserted.\n   * @param start Index at which the text is insert.\n   * @param text Text to be inserted.\n   */\n  insertText(sourceFile: ts.SourceFile, index: number, text: string): void {\n    this._trackChange(sourceFile, {start: index, text});\n  }\n\n  /**\n   * Replaces text within a file.\n   * @param sourceFile File in which to replace the text.\n   * @param start Index from which to replace the text.\n   * @param removeLength Length of the text being replaced.\n   * @param text Text to be inserted instead of the old one.\n   */\n  replaceText(sourceFile: ts.SourceFile, start: number, removeLength: number, text: string): void {\n    this._trackChange(sourceFile, {start, removeLength, text});\n  }\n\n  /**\n   * Replaces the text of an AST node with a new one.\n   * @param oldNode Node to be replaced.\n   * @param newNode New node to be inserted.\n   * @param emitHint Hint when formatting the text of the new node.\n   * @param sourceFileWhenPrinting File to use when printing out the new node. This is important\n   * when copying nodes from one file to another, because TypeScript might not output literal nodes\n   * without it.\n   */\n  replaceNode(\n      oldNode: ts.Node, newNode: ts.Node, emitHint = ts.EmitHint.Unspecified,\n      sourceFileWhenPrinting?: ts.SourceFile): void {\n    const sourceFile = oldNode.getSourceFile();\n    this.replaceText(\n        sourceFile, oldNode.getStart(), oldNode.getWidth(),\n        this._printer.printNode(emitHint, newNode, sourceFileWhenPrinting || sourceFile));\n  }\n\n  /**\n   * Removes the text of an AST node from a file.\n   * @param node Node whose text should be removed.\n   */\n  removeNode(node: ts.Node): void {\n    this._trackChange(\n        node.getSourceFile(), {start: node.getStart(), removeLength: node.getWidth(), text: ''});\n  }\n\n  /**\n   * Adds an import to a file.\n   * @param sourceFile File to which to add the import.\n   * @param symbolName Symbol being imported.\n   * @param moduleName Module from which the symbol is imported.\n   */\n  addImport(\n      sourceFile: ts.SourceFile, symbolName: string, moduleName: string, alias: string|null = null,\n      keepSymbolName = false): ts.Expression {\n    if (this._importRemapper) {\n      moduleName = this._importRemapper(moduleName, sourceFile.fileName);\n    }\n\n    // It's common for paths to be manipulated with Node's `path` utilties which\n    // can yield a path with back slashes. Normalize them since outputting such\n    // paths will also cause TS to escape the forward slashes.\n    moduleName = normalizePath(moduleName);\n\n    return this._importManager.addImportToSourceFile(\n        sourceFile, symbolName, moduleName, alias, false, keepSymbolName);\n  }\n\n  /**\n   * Gets the changes that should be applied to all the files in the migration.\n   * The changes are sorted in the order in which they should be applied.\n   */\n  recordChanges(): ChangesByFile {\n    this._importManager.recordChanges();\n    return this._changes;\n  }\n\n  /**\n   * Clear the tracked changes\n   */\n  clearChanges(): void {\n    this._changes.clear();\n  }\n\n  /**\n   * Adds a change to a `ChangesByFile` map.\n   * @param file File that the change is associated with.\n   * @param change Change to be added.\n   */\n  private _trackChange(file: ts.SourceFile, change: PendingChange): void {\n    const changes = this._changes.get(file);\n\n    if (changes) {\n      // Insert the changes in reverse so that they're applied in reverse order.\n      // This ensures that the offsets of subsequent changes aren't affected by\n      // previous changes changing the file's text.\n      const insertIndex = changes.findIndex(current => current.start <= change.start);\n\n      if (insertIndex === -1) {\n        changes.push(change);\n      } else {\n        changes.splice(insertIndex, 0, change);\n      }\n    } else {\n      this._changes.set(file, [change]);\n    }\n  }\n}\n\n/** Normalizes a path to use posix separators. */\nexport function normalizePath(path: string): string {\n  return path.replace(/\\\\/g, '/');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {dirname, resolve} from 'path';\nimport ts from 'typescript';\n\n/** Update recorder for managing imports. */\nexport interface ImportManagerUpdateRecorder {\n  addNewImport(start: number, importText: string): void;\n  updateExistingImport(namedBindings: ts.NamedImports, newNamedBindings: string): void;\n}\n\n/** Possible types of quotes for imports. */\nconst enum QuoteStyle {\n  Single,\n  Double,\n}\n\n/**\n * Import manager that can be used to add TypeScript imports to given source\n * files. The manager ensures that multiple transformations are applied properly\n * without shifted offsets and that similar existing import declarations are re-used.\n */\nexport class ImportManager {\n  /** Map of import declarations that need to be updated to include the given symbols. */\n  private updatedImports =\n      new Map<ts.ImportDeclaration, {propertyName?: ts.Identifier, importName: ts.Identifier}[]>();\n  /** Map of source-files and their previously used identifier names. */\n  private usedIdentifierNames = new Map<ts.SourceFile, string[]>();\n  /** Map of source files and the new imports that have to be added to them. */\n  private newImports: Map<ts.SourceFile, {\n    importStartIndex: number,\n    defaultImports: Map<string, ts.Identifier>,\n    namedImports: Map<string, ts.ImportSpecifier[]>,\n  }> = new Map();\n  /** Map between a file and the implied quote style for imports. */\n  private quoteStyles: Record<string, QuoteStyle> = {};\n\n  /**\n   * Array of previously resolved symbol imports. Cache can be re-used to return\n   * the same identifier without checking the source-file again.\n   */\n  private importCache: {\n    sourceFile: ts.SourceFile,\n    symbolName: string|null,\n    alias: string|null,\n    moduleName: string,\n    identifier: ts.Identifier\n  }[] = [];\n\n  constructor(\n      private getUpdateRecorder: (sf: ts.SourceFile) => ImportManagerUpdateRecorder,\n      private printer: ts.Printer) {}\n\n  /**\n   * Adds an import to the given source-file and returns the TypeScript\n   * identifier that can be used to access the newly imported symbol.\n   */\n  addImportToSourceFile(\n      sourceFile: ts.SourceFile, symbolName: string|null, moduleName: string,\n      alias: string|null = null, typeImport = false, keepSymbolName = false): ts.Expression {\n    const sourceDir = dirname(sourceFile.fileName);\n    let importStartIndex = 0;\n    let existingImport: ts.ImportDeclaration|null = null;\n\n    // In case the given import has been already generated previously, we just return\n    // the previous generated identifier in order to avoid duplicate generated imports.\n    const cachedImport = this.importCache.find(\n        c => c.sourceFile === sourceFile && c.symbolName === symbolName &&\n            c.moduleName === moduleName && c.alias === alias);\n    if (cachedImport) {\n      return cachedImport.identifier;\n    }\n\n    // Walk through all source-file top-level statements and search for import declarations\n    // that already match the specified \"moduleName\" and can be updated to import the\n    // given symbol. If no matching import can be found, the last import in the source-file\n    // will be used as starting point for a new import that will be generated.\n    for (let i = sourceFile.statements.length - 1; i >= 0; i--) {\n      const statement = sourceFile.statements[i];\n\n      if (!ts.isImportDeclaration(statement) || !ts.isStringLiteral(statement.moduleSpecifier) ||\n          !statement.importClause) {\n        continue;\n      }\n\n      if (importStartIndex === 0) {\n        importStartIndex = this._getEndPositionOfNode(statement);\n      }\n\n      const moduleSpecifier = statement.moduleSpecifier.text;\n\n      if (moduleSpecifier.startsWith('.') &&\n              resolve(sourceDir, moduleSpecifier) !== resolve(sourceDir, moduleName) ||\n          moduleSpecifier !== moduleName) {\n        continue;\n      }\n\n      if (statement.importClause.namedBindings) {\n        const namedBindings = statement.importClause.namedBindings;\n\n        // In case a \"Type\" symbol is imported, we can't use namespace imports\n        // because these only export symbols available at runtime (no types)\n        if (ts.isNamespaceImport(namedBindings) && !typeImport) {\n          return ts.factory.createPropertyAccessExpression(\n              ts.factory.createIdentifier(namedBindings.name.text),\n              ts.factory.createIdentifier(alias || symbolName || 'default'));\n        } else if (ts.isNamedImports(namedBindings) && symbolName) {\n          const existingElement = namedBindings.elements.find(e => {\n            // TODO(crisbeto): if an alias conflicts with an existing import, it may cause invalid\n            // code to be generated. This is unlikely, but we may want to revisit it in the future.\n            if (alias) {\n              return e.propertyName && e.name.text === alias && e.propertyName.text === symbolName;\n            }\n            return e.propertyName ? e.propertyName.text === symbolName : e.name.text === symbolName;\n          });\n\n          if (existingElement) {\n            return ts.factory.createIdentifier(existingElement.name.text);\n          }\n\n          // In case the symbol could not be found in an existing import, we\n          // keep track of the import declaration as it can be updated to include\n          // the specified symbol name without having to create a new import.\n          existingImport = statement;\n        }\n      } else if (statement.importClause.name && !symbolName) {\n        return ts.factory.createIdentifier(statement.importClause.name.text);\n      }\n    }\n\n    if (existingImport) {\n      const {propertyName, name} =\n          this._getImportParts(sourceFile, symbolName!, alias, keepSymbolName);\n\n      // Since it can happen that multiple classes need to be imported within the\n      // specified source file and we want to add the identifiers to the existing\n      // import declaration, we need to keep track of the updated import declarations.\n      // We can't directly update the import declaration for each identifier as this\n      // would throw off the recorder offsets. We need to keep track of the new identifiers\n      // for the import and perform the import transformation as batches per source-file.\n      this.updatedImports.set(\n          existingImport,\n          (this.updatedImports.get(existingImport) || []).concat({propertyName, importName: name}));\n\n      // Keep track of all updated imports so that we don't generate duplicate\n      // similar imports as these can't be statically analyzed in the source-file yet.\n      this.importCache.push({sourceFile, moduleName, symbolName, alias, identifier: name});\n\n      return name;\n    }\n\n    let identifier: ts.Identifier|null = null;\n\n    if (!this.newImports.has(sourceFile)) {\n      this.newImports.set(sourceFile, {\n        importStartIndex,\n        defaultImports: new Map(),\n        namedImports: new Map(),\n      });\n    }\n\n    if (symbolName) {\n      const {propertyName, name} =\n          this._getImportParts(sourceFile, symbolName, alias, keepSymbolName);\n      const importMap = this.newImports.get(sourceFile)!.namedImports;\n      identifier = name;\n\n      if (!importMap.has(moduleName)) {\n        importMap.set(moduleName, []);\n      }\n\n      importMap.get(moduleName)!.push(ts.factory.createImportSpecifier(false, propertyName, name));\n    } else {\n      const importMap = this.newImports.get(sourceFile)!.defaultImports;\n      identifier = this._getUniqueIdentifier(sourceFile, 'defaultExport');\n      importMap.set(moduleName, identifier);\n    }\n\n    // Keep track of all generated imports so that we don't generate duplicate\n    // similar imports as these can't be statically analyzed in the source-file yet.\n    this.importCache.push({sourceFile, symbolName, moduleName, alias, identifier});\n\n    return identifier;\n  }\n\n  /**\n   * Stores the collected import changes within the appropriate update recorders. The\n   * updated imports can only be updated *once* per source-file because previous updates\n   * could otherwise shift the source-file offsets.\n   */\n  recordChanges() {\n    this.updatedImports.forEach((expressions, importDecl) => {\n      const sourceFile = importDecl.getSourceFile();\n      const recorder = this.getUpdateRecorder(sourceFile);\n      const namedBindings = importDecl.importClause!.namedBindings as ts.NamedImports;\n      const newNamedBindings = ts.factory.updateNamedImports(\n          namedBindings,\n          namedBindings.elements.concat(expressions.map(\n              ({propertyName, importName}) =>\n                  ts.factory.createImportSpecifier(false, propertyName, importName))));\n\n      const newNamedBindingsText =\n          this.printer.printNode(ts.EmitHint.Unspecified, newNamedBindings, sourceFile);\n      recorder.updateExistingImport(namedBindings, newNamedBindingsText);\n    });\n\n    this.newImports.forEach(({importStartIndex, defaultImports, namedImports}, sourceFile) => {\n      const recorder = this.getUpdateRecorder(sourceFile);\n      const useSingleQuotes = this._getQuoteStyle(sourceFile) === QuoteStyle.Single;\n\n      defaultImports.forEach((identifier, moduleName) => {\n        const newImport = ts.factory.createImportDeclaration(\n            undefined, ts.factory.createImportClause(false, identifier, undefined),\n            ts.factory.createStringLiteral(moduleName, useSingleQuotes));\n\n        recorder.addNewImport(\n            importStartIndex, this._getNewImportText(importStartIndex, newImport, sourceFile));\n      });\n\n      namedImports.forEach((specifiers, moduleName) => {\n        const newImport = ts.factory.createImportDeclaration(\n            undefined,\n            ts.factory.createImportClause(\n                false, undefined, ts.factory.createNamedImports(specifiers)),\n            ts.factory.createStringLiteral(moduleName, useSingleQuotes));\n\n        recorder.addNewImport(\n            importStartIndex, this._getNewImportText(importStartIndex, newImport, sourceFile));\n      });\n    });\n  }\n\n  /** Gets an unique identifier with a base name for the given source file. */\n  private _getUniqueIdentifier(sourceFile: ts.SourceFile, baseName: string): ts.Identifier {\n    if (this.isUniqueIdentifierName(sourceFile, baseName)) {\n      this._recordUsedIdentifier(sourceFile, baseName);\n      return ts.factory.createIdentifier(baseName);\n    }\n\n    let name = null;\n    let counter = 1;\n    do {\n      name = `${baseName}_${counter++}`;\n    } while (!this.isUniqueIdentifierName(sourceFile, name));\n\n    this._recordUsedIdentifier(sourceFile, name!);\n    return ts.factory.createIdentifier(name!);\n  }\n\n  /**\n   * Checks whether the specified identifier name is used within the given\n   * source file.\n   */\n  private isUniqueIdentifierName(sourceFile: ts.SourceFile, name: string) {\n    if (this.usedIdentifierNames.has(sourceFile) &&\n        this.usedIdentifierNames.get(sourceFile)!.indexOf(name) !== -1) {\n      return false;\n    }\n\n    // Walk through the source file and search for an identifier matching\n    // the given name. In that case, it's not guaranteed that this name\n    // is unique in the given declaration scope and we just return false.\n    const nodeQueue: ts.Node[] = [sourceFile];\n    while (nodeQueue.length) {\n      const node = nodeQueue.shift()!;\n      if (ts.isIdentifier(node) && node.text === name &&\n          // Identifiers that are aliased in an import aren't\n          // problematic since they're used under a different name.\n          (!ts.isImportSpecifier(node.parent) || node.parent.propertyName !== node)) {\n        return false;\n      }\n      nodeQueue.push(...node.getChildren());\n    }\n    return true;\n  }\n\n  private _recordUsedIdentifier(sourceFile: ts.SourceFile, identifierName: string) {\n    this.usedIdentifierNames.set(\n        sourceFile, (this.usedIdentifierNames.get(sourceFile) || []).concat(identifierName));\n  }\n\n  /**\n   * Determines the full end of a given node. By default the end position of a node is\n   * before all trailing comments. This could mean that generated imports shift comments.\n   */\n  private _getEndPositionOfNode(node: ts.Node) {\n    const nodeEndPos = node.getEnd();\n    const commentRanges = ts.getTrailingCommentRanges(node.getSourceFile().text, nodeEndPos);\n    if (!commentRanges || !commentRanges.length) {\n      return nodeEndPos;\n    }\n    return commentRanges[commentRanges.length - 1]!.end;\n  }\n\n  /** Gets the text that should be added to the file for a newly-created import declaration. */\n  private _getNewImportText(\n      importStartIndex: number, newImport: ts.ImportDeclaration,\n      sourceFile: ts.SourceFile): string {\n    const text = this.printer.printNode(ts.EmitHint.Unspecified, newImport, sourceFile);\n\n    // If the import is generated at the start of the source file, we want to add\n    // a new-line after the import. Otherwise if the import is generated after an\n    // existing import, we need to prepend a new-line so that the import is not on\n    // the same line as the existing import anchor\n    return importStartIndex === 0 ? `${text}\\n` : `\\n${text}`;\n  }\n\n  /**\n   * Gets the different parts necessary to construct an import specifier.\n   * @param sourceFile File in which the import is being inserted.\n   * @param symbolName Name of the symbol.\n   * @param alias Alias that the symbol may be available under.\n   * @returns Object containing the different parts. E.g. `{name: 'alias', propertyName: 'name'}`\n   * would correspond to `import {name as alias}` while `{name: 'name', propertyName: undefined}`\n   * corresponds to `import {name}`.\n   */\n  private _getImportParts(\n      sourceFile: ts.SourceFile, symbolName: string, alias: string|null, keepSymbolName: boolean) {\n    const symbolIdentifier = ts.factory.createIdentifier(symbolName);\n    const aliasIdentifier = alias ? ts.factory.createIdentifier(alias) : null;\n    const generatedUniqueIdentifier = this._getUniqueIdentifier(sourceFile, alias || symbolName);\n    const needsGeneratedUniqueName = generatedUniqueIdentifier.text !== (alias || symbolName);\n    let propertyName: ts.Identifier|undefined;\n    let name: ts.Identifier;\n\n    if (needsGeneratedUniqueName && !keepSymbolName) {\n      propertyName = symbolIdentifier;\n      name = generatedUniqueIdentifier;\n    } else if (aliasIdentifier) {\n      propertyName = symbolIdentifier;\n      name = aliasIdentifier;\n    } else {\n      name = symbolIdentifier;\n    }\n\n    return {propertyName, name};\n  }\n\n  /** Gets the quote style that is used for a file's imports. */\n  private _getQuoteStyle(sourceFile: ts.SourceFile): QuoteStyle {\n    if (!this.quoteStyles.hasOwnProperty(sourceFile.fileName)) {\n      let quoteStyle: QuoteStyle|undefined;\n\n      // Walk through the top-level imports and try to infer the quotes.\n      for (const statement of sourceFile.statements) {\n        if (ts.isImportDeclaration(statement) &&\n            ts.isStringLiteralLike(statement.moduleSpecifier)) {\n          // Use `getText` instead of the actual text since it includes the quotes.\n          quoteStyle = statement.moduleSpecifier.getText().trim().startsWith('\"') ?\n              QuoteStyle.Double :\n              QuoteStyle.Single;\n          break;\n        }\n      }\n\n      // Otherwise fall back to single quotes.\n      this.quoteStyles[sourceFile.fileName] = quoteStyle ?? QuoteStyle.Single;\n    }\n\n    return this.quoteStyles[sourceFile.fileName];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport ts from 'typescript';\n\nexport type Import = {\n  name: string,\n  importModule: string,\n  node: ts.ImportDeclaration\n};\n\n/** Gets import information about the specified identifier by using the Type checker. */\nexport function getImportOfIdentifier(typeChecker: ts.TypeChecker, node: ts.Identifier): Import|\n    null {\n  const symbol = typeChecker.getSymbolAtLocation(node);\n\n  if (!symbol || symbol.declarations === undefined || !symbol.declarations.length) {\n    return null;\n  }\n\n  const decl = symbol.declarations[0];\n\n  if (!ts.isImportSpecifier(decl)) {\n    return null;\n  }\n\n  const importDecl = decl.parent.parent.parent;\n\n  if (!ts.isStringLiteral(importDecl.moduleSpecifier)) {\n    return null;\n  }\n\n  return {\n    // Handles aliased imports: e.g. \"import {Component as myComp} from ...\";\n    name: decl.propertyName ? decl.propertyName.text : decl.name.text,\n    importModule: importDecl.moduleSpecifier.text,\n    node: importDecl\n  };\n}\n\n\n/**\n * Gets a top-level import specifier with a specific name that is imported from a particular module.\n * E.g. given a file that looks like:\n *\n * ```\n * import { Component, Directive } from '@angular/core';\n * import { Foo } from './foo';\n * ```\n *\n * Calling `getImportSpecifier(sourceFile, '@angular/core', 'Directive')` will yield the node\n * referring to `Directive` in the top import.\n *\n * @param sourceFile File in which to look for imports.\n * @param moduleName Name of the import's module.\n * @param specifierName Original name of the specifier to look for. Aliases will be resolved to\n *    their original name.\n */\nexport function getImportSpecifier(\n    sourceFile: ts.SourceFile, moduleName: string|RegExp,\n    specifierName: string): ts.ImportSpecifier|null {\n  return getImportSpecifiers(sourceFile, moduleName, [specifierName])[0] ?? null;\n}\n\nexport function getImportSpecifiers(\n    sourceFile: ts.SourceFile, moduleName: string|RegExp,\n    specifierNames: string[]): ts.ImportSpecifier[] {\n  const matches: ts.ImportSpecifier[] = [];\n  for (const node of sourceFile.statements) {\n    if (ts.isImportDeclaration(node) && ts.isStringLiteral(node.moduleSpecifier)) {\n      const isMatch = typeof moduleName === 'string' ? node.moduleSpecifier.text === moduleName :\n                                                       moduleName.test(node.moduleSpecifier.text);\n      const namedBindings = node.importClause?.namedBindings;\n      if (isMatch && namedBindings && ts.isNamedImports(namedBindings)) {\n        for (const specifierName of specifierNames) {\n          const match = findImportSpecifier(namedBindings.elements, specifierName);\n          if (match) {\n            matches.push(match);\n          }\n        }\n      }\n    }\n  }\n  return matches;\n}\n\n\n/**\n * Replaces an import inside a named imports node with a different one.\n *\n * @param node Node that contains the imports.\n * @param existingImport Import that should be replaced.\n * @param newImportName Import that should be inserted.\n */\nexport function replaceImport(\n    node: ts.NamedImports, existingImport: string, newImportName: string) {\n  const isAlreadyImported = findImportSpecifier(node.elements, newImportName);\n  if (isAlreadyImported) {\n    return node;\n  }\n\n  const existingImportNode = findImportSpecifier(node.elements, existingImport);\n  if (!existingImportNode) {\n    return node;\n  }\n\n  const importPropertyName =\n      existingImportNode.propertyName ? ts.factory.createIdentifier(newImportName) : undefined;\n  const importName = existingImportNode.propertyName ? existingImportNode.name :\n                                                       ts.factory.createIdentifier(newImportName);\n\n  return ts.factory.updateNamedImports(node, [\n    ...node.elements.filter(current => current !== existingImportNode),\n    // Create a new import while trying to preserve the alias of the old one.\n    ts.factory.createImportSpecifier(false, importPropertyName, importName)\n  ]);\n}\n\n/**\n * Removes a symbol from the named imports and updates a node\n * that represents a given named imports.\n *\n * @param node Node that contains the imports.\n * @param symbol Symbol that should be removed.\n * @returns An updated node (ts.NamedImports).\n */\nexport function removeSymbolFromNamedImports(node: ts.NamedImports, symbol: ts.ImportSpecifier) {\n  return ts.factory.updateNamedImports(node, [\n    ...node.elements.filter(current => current !== symbol),\n  ]);\n}\n\n/** Finds an import specifier with a particular name. */\nexport function findImportSpecifier(\n    nodes: ts.NodeArray<ts.ImportSpecifier>, specifierName: string): ts.ImportSpecifier|undefined {\n  return nodes.find(element => {\n    const {name, propertyName} = element;\n    return propertyName ? propertyName.text === specifierName : name.text === specifierName;\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport ts from 'typescript';\n\n/** Checks whether the given TypeScript node has the specified modifier set. */\nexport function hasModifier(node: ts.Node, modifierKind: ts.SyntaxKind) {\n  return ts.canHaveModifiers(node) && !!node.modifiers &&\n      node.modifiers.some(m => m.kind === modifierKind);\n}\n\n/** Find the closest parent node of a particular kind. */\nexport function closestNode<T extends ts.Node>(node: ts.Node, predicate: (n: ts.Node) => n is T): T|\n    null {\n  let current = node.parent;\n\n  while (current && !ts.isSourceFile(current)) {\n    if (predicate(current)) {\n      return current;\n    }\n    current = current.parent;\n  }\n\n  return null;\n}\n\n/**\n * Checks whether a particular node is part of a null check. E.g. given:\n * `foo.bar ? foo.bar.value : null` the null check would be `foo.bar`.\n */\nexport function isNullCheck(node: ts.Node): boolean {\n  if (!node.parent) {\n    return false;\n  }\n\n  // `foo.bar && foo.bar.value` where `node` is `foo.bar`.\n  if (ts.isBinaryExpression(node.parent) && node.parent.left === node) {\n    return true;\n  }\n\n  // `foo.bar && foo.bar.parent && foo.bar.parent.value`\n  // where `node` is `foo.bar`.\n  if (node.parent.parent && ts.isBinaryExpression(node.parent.parent) &&\n      node.parent.parent.left === node.parent) {\n    return true;\n  }\n\n  // `if (foo.bar) {...}` where `node` is `foo.bar`.\n  if (ts.isIfStatement(node.parent) && node.parent.expression === node) {\n    return true;\n  }\n\n  // `foo.bar ? foo.bar.value : null` where `node` is `foo.bar`.\n  if (ts.isConditionalExpression(node.parent) && node.parent.condition === node) {\n    return true;\n  }\n\n  return false;\n}\n\n/** Checks whether a property access is safe (e.g. `foo.parent?.value`). */\nexport function isSafeAccess(node: ts.Node): boolean {\n  return node.parent != null && ts.isPropertyAccessExpression(node.parent) &&\n      node.parent.expression === node && node.parent.questionDotToken != null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;AAQA,wBAA8D;AAC9D,IAAAA,eAAuB;;;ACDvB,kBAAqD;AAOrD,SAAsB,wBAAwB,MAAU;;AAItD,UAAM,aAAa,oBAAI,IAAG;AAC1B,UAAM,YAAY,oBAAI,IAAG;AAEzB,UAAM,YAAY,MAAM,aAAa,IAAI;AACzC,eAAW,CAAC,EAAE,OAAO,KAAK,UAAU,UAAU;AAC5C,iBAAW,CAAC,MAAM,MAAM,KAAK,QAAQ,SAAS;AAC5C,YAAI,SAAS,WAAW,SAAS,QAAQ;AACvC;QACF;AAEA,mBAAW,CAAC,EAAE,OAAO,KAAK,iBAAiB,MAAM,GAAG;AAClD,gBAAM,WAAW,QAAQ;AAEzB,cAAI,OAAO,aAAa,YAAY,CAAC,KAAK,OAAO,QAAQ,GAAG;AAC1D;UACF;AAEA,cAAI,SAAS,SAAS;AACpB,uBAAW,QAAI,uBAAU,QAAQ,CAAC;UACpC,OAAO;AACL,sBAAU,QAAI,uBAAU,QAAQ,CAAC;UACnC;QACF;MACF;IACF;AAEA,WAAO;MACL,YAAY,CAAC,GAAG,UAAU;MAC1B,WAAW,CAAC,GAAG,SAAS;;EAE5B;;AAGA,UACI,iBAAiB,QAAmC;AAEtD,MAAI,OAAO,SAAS;AAClB,UAAM,CAAC,QAAW,OAAO,OAAO;EAClC;AAEA,MAAI,CAAC,OAAO,gBAAgB;AAC1B;EACF;AAEA,aAAW,CAAC,MAAM,OAAO,KAAK,OAAO,QAAQ,OAAO,cAAc,GAAG;AACnE,QAAI,SAAS;AACX,YAAM,CAAC,MAAM,OAAO;IACtB;EACF;AACF;AAEA,SAAS,WAAW,MAAU;AAC5B,SAAO;IACC,SAASC,OAAY;;AACzB,cAAM,OAAO,KAAK,KAAKA,KAAI;AAC3B,YAAI,CAAC,MAAM;AACT,gBAAM,IAAI,MAAM,iBAAiB;QACnC;AAEA,eAAO,sBAAU,mBAAmB,IAAI;MAC1C;;IACM,UAAUA,OAAc,MAAY;;AACxC,eAAO,KAAK,UAAUA,OAAM,IAAI;MAClC;;IACM,YAAYA,OAAY;;AAK5B,eAAO,CAAC,KAAK,OAAOA,KAAI,KAAK,KAAK,OAAOA,KAAI,EAAE,SAAS,SAAS;MACnE;;IACM,OAAOA,OAAY;;AACvB,eAAO,KAAK,OAAOA,KAAI;MACzB;;;AAEJ;AAEA,SAAe,aAAa,MAAU;;AACpC,UAAM,OAAO,WAAW,IAAI;AAC5B,UAAM,EAAC,UAAS,IAAI,MAAM,uBAAW,cAAc,KAAK,IAAI;AAE5D,WAAO;EACT;;;;AC7FA,kBAAyC;AACzC,IAAAC,qBAAe;;;ACDf,WAAsB;AACtB,wBAAe;AAET,SAAU,kBAAkB,cAAsB,UAAgB;AACtE,QAAM,EAAC,OAAM,IAAI,kBAAAC,QAAG,eAAe,cAAc,kBAAAA,QAAG,IAAI,QAAQ;AAChE,QAAM,kBAAkB;IACtB,2BAA2B,kBAAAA,QAAG,IAAI;IAClC,YAAY,kBAAAA,QAAG,IAAI;IACnB,eAAe,kBAAAA,QAAG,IAAI;IACtB,UAAU,kBAAAA,QAAG,IAAI;;AAMnB,MAAI,CAAM,gBAAW,QAAQ,GAAG;AAC9B,UAAM,MAAM,mDAAmD;EACjE;AAEA,SAAO,kBAAAA,QAAG,2BAA2B,QAAQ,iBAAiB,UAAU,CAAA,CAAE;AAC5E;;;ADHM,SAAU,uBACZ,MAAY,cAAsB,UAAkB,cACpD,iBAA0B;AAC5B,QAAM,EAAC,WAAW,SAAS,KAAI,IAC3B,qBAAqB,MAAM,cAAc,UAAU,cAAc,eAAe;AACpF,SAAO,mBAAAC,QAAG,cAAc,WAAW,SAAS,IAAI;AAClD;AAYM,SAAU,qBACZ,MAAY,cAAsB,UAAkB,cACpD,iBAA4B,iBAAoC;AAIlE,qBAAe,qBAAQ,UAAU,YAAY;AAC7C,QAAM,SAAS,kBAAkB,kBAAc,qBAAQ,YAAY,CAAC;AACpE,QAAM,UAAU,kBAAkB,kCAAI,OAAO,UAAY,mBAAmB,OAAO;AACnF,QAAM,OAAO,4BAA4B,MAAM,SAAS,UAAU,YAAY;AAC9E,SAAO,EAAC,WAAW,OAAO,UAAU,OAAO,mBAAmB,CAAA,CAAE,GAAG,SAAS,KAAI;AAClF;AAEA,SAAS,4BACL,MAAY,SAA6B,UACzC,UAAyB;AAC3B,QAAM,OAAO,mBAAAA,QAAG,mBAAmB,SAAS,IAAI;AAChD,QAAM,kBAAkB,KAAK;AAM7B,OAAK,WAAW,cAAW;AA1D7B;AA2DI,UAAM,uBAAmB,sBAAS,UAAU,QAAQ;AACpD,QAAI,SAA2B,qCAAW;AAE1C,QAAI,OAAO,WAAW,UAAU;AAG9B,eAAS,iBAAiB,WAAW,IAAI,IAAI,gBAAgB,KAAK,MAAM,QAAQ,KACnC,UAAK,KAAK,gBAAgB,MAA1B,mBAA6B;IAC5E;AAKA,WAAO,OAAO,WAAW,WAAW,OAAO,QAAQ,WAAW,EAAE,IAAI;EACtE;AAEA,SAAO;AACT;AAQM,SAAU,eACZ,UAAkB,YAA2B,SAAmB;AAElE,MAAI,WAAW,SAAS,SAAS,iBAAiB,KAAK,WAAW,qBAC9D,QAAQ,gCAAgC,UAAU,GAAG;AACvD,WAAO;EACT;AAOA,SAAO,KAAC,sBAAS,UAAU,WAAW,QAAQ,EAAE,WAAW,IAAI;AACjE;;;AElGA,IAAAC,qBAAe;;;ACAf,IAAAC,qBAAe;;;ACAf,IAAAC,eAA+B;AAC/B,IAAAC,qBAAe;AAmBT,IAAO,gBAAP,MAAoB;EA2BxB,YACY,mBACA,SAAmB;AADnB;AACA;AA3BJ,0CACJ,oBAAI,IAAG;AAEH,+CAAsB,oBAAI,IAAG;AAE7B,sCAIH,oBAAI,IAAG;AAEJ,uCAA0C,CAAA;AAM1C,uCAMF,CAAA;AAGM,SAAA,oBAAA;AACA,SAAA,UAAA;EAAsB;EAMlC,sBACI,YAA2B,YAAyB,YACpD,QAAqB,MAAM,aAAa,OAAO,iBAAiB,OAAK;AACvE,UAAM,gBAAY,sBAAQ,WAAW,QAAQ;AAC7C,QAAI,mBAAmB;AACvB,QAAI,iBAA4C;AAIhD,UAAM,eAAe,KAAK,YAAY,KAClC,OAAK,EAAE,eAAe,cAAc,EAAE,eAAe,cACjD,EAAE,eAAe,cAAc,EAAE,UAAU,KAAK;AACxD,QAAI,cAAc;AAChB,aAAO,aAAa;IACtB;AAMA,aAAS,IAAI,WAAW,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1D,YAAM,YAAY,WAAW,WAAW;AAExC,UAAI,CAAC,mBAAAC,QAAG,oBAAoB,SAAS,KAAK,CAAC,mBAAAA,QAAG,gBAAgB,UAAU,eAAe,KACnF,CAAC,UAAU,cAAc;AAC3B;MACF;AAEA,UAAI,qBAAqB,GAAG;AAC1B,2BAAmB,KAAK,sBAAsB,SAAS;MACzD;AAEA,YAAM,kBAAkB,UAAU,gBAAgB;AAElD,UAAI,gBAAgB,WAAW,GAAG,SAC1B,sBAAQ,WAAW,eAAe,UAAM,sBAAQ,WAAW,UAAU,KACzE,oBAAoB,YAAY;AAClC;MACF;AAEA,UAAI,UAAU,aAAa,eAAe;AACxC,cAAM,gBAAgB,UAAU,aAAa;AAI7C,YAAI,mBAAAA,QAAG,kBAAkB,aAAa,KAAK,CAAC,YAAY;AACtD,iBAAO,mBAAAA,QAAG,QAAQ,+BACd,mBAAAA,QAAG,QAAQ,iBAAiB,cAAc,KAAK,IAAI,GACnD,mBAAAA,QAAG,QAAQ,iBAAiB,SAAS,cAAc,SAAS,CAAC;QACnE,WAAW,mBAAAA,QAAG,eAAe,aAAa,KAAK,YAAY;AACzD,gBAAM,kBAAkB,cAAc,SAAS,KAAK,OAAI;AAGtD,gBAAI,OAAO;AACT,qBAAO,EAAE,gBAAgB,EAAE,KAAK,SAAS,SAAS,EAAE,aAAa,SAAS;YAC5E;AACA,mBAAO,EAAE,eAAe,EAAE,aAAa,SAAS,aAAa,EAAE,KAAK,SAAS;UAC/E,CAAC;AAED,cAAI,iBAAiB;AACnB,mBAAO,mBAAAA,QAAG,QAAQ,iBAAiB,gBAAgB,KAAK,IAAI;UAC9D;AAKA,2BAAiB;QACnB;MACF,WAAW,UAAU,aAAa,QAAQ,CAAC,YAAY;AACrD,eAAO,mBAAAA,QAAG,QAAQ,iBAAiB,UAAU,aAAa,KAAK,IAAI;MACrE;IACF;AAEA,QAAI,gBAAgB;AAClB,YAAM,EAAC,cAAc,KAAI,IACrB,KAAK,gBAAgB,YAAY,YAAa,OAAO,cAAc;AAQvE,WAAK,eAAe,IAChB,iBACC,KAAK,eAAe,IAAI,cAAc,KAAK,CAAA,GAAI,OAAO,EAAC,cAAc,YAAY,KAAI,CAAC,CAAC;AAI5F,WAAK,YAAY,KAAK,EAAC,YAAY,YAAY,YAAY,OAAO,YAAY,KAAI,CAAC;AAEnF,aAAO;IACT;AAEA,QAAI,aAAiC;AAErC,QAAI,CAAC,KAAK,WAAW,IAAI,UAAU,GAAG;AACpC,WAAK,WAAW,IAAI,YAAY;QAC9B;QACA,gBAAgB,oBAAI,IAAG;QACvB,cAAc,oBAAI,IAAG;OACtB;IACH;AAEA,QAAI,YAAY;AACd,YAAM,EAAC,cAAc,KAAI,IACrB,KAAK,gBAAgB,YAAY,YAAY,OAAO,cAAc;AACtE,YAAM,YAAY,KAAK,WAAW,IAAI,UAAU,EAAG;AACnD,mBAAa;AAEb,UAAI,CAAC,UAAU,IAAI,UAAU,GAAG;AAC9B,kBAAU,IAAI,YAAY,CAAA,CAAE;MAC9B;AAEA,gBAAU,IAAI,UAAU,EAAG,KAAK,mBAAAA,QAAG,QAAQ,sBAAsB,OAAO,cAAc,IAAI,CAAC;IAC7F,OAAO;AACL,YAAM,YAAY,KAAK,WAAW,IAAI,UAAU,EAAG;AACnD,mBAAa,KAAK,qBAAqB,YAAY,eAAe;AAClE,gBAAU,IAAI,YAAY,UAAU;IACtC;AAIA,SAAK,YAAY,KAAK,EAAC,YAAY,YAAY,YAAY,OAAO,WAAU,CAAC;AAE7E,WAAO;EACT;EAOA,gBAAa;AACX,SAAK,eAAe,QAAQ,CAAC,aAAa,eAAc;AACtD,YAAM,aAAa,WAAW,cAAa;AAC3C,YAAM,WAAW,KAAK,kBAAkB,UAAU;AAClD,YAAM,gBAAgB,WAAW,aAAc;AAC/C,YAAM,mBAAmB,mBAAAA,QAAG,QAAQ,mBAChC,eACA,cAAc,SAAS,OAAO,YAAY,IACtC,CAAC,EAAC,cAAc,WAAU,MACtB,mBAAAA,QAAG,QAAQ,sBAAsB,OAAO,cAAc,UAAU,CAAC,CAAC,CAAC;AAE/E,YAAM,uBACF,KAAK,QAAQ,UAAU,mBAAAA,QAAG,SAAS,aAAa,kBAAkB,UAAU;AAChF,eAAS,qBAAqB,eAAe,oBAAoB;IACnE,CAAC;AAED,SAAK,WAAW,QAAQ,CAAC,EAAC,kBAAkB,gBAAgB,aAAY,GAAG,eAAc;AACvF,YAAM,WAAW,KAAK,kBAAkB,UAAU;AAClD,YAAM,kBAAkB,KAAK,eAAe,UAAU,MAAC;AAEvD,qBAAe,QAAQ,CAAC,YAAY,eAAc;AAChD,cAAM,YAAY,mBAAAA,QAAG,QAAQ,wBACzB,QAAW,mBAAAA,QAAG,QAAQ,mBAAmB,OAAO,YAAY,MAAS,GACrE,mBAAAA,QAAG,QAAQ,oBAAoB,YAAY,eAAe,CAAC;AAE/D,iBAAS,aACL,kBAAkB,KAAK,kBAAkB,kBAAkB,WAAW,UAAU,CAAC;MACvF,CAAC;AAED,mBAAa,QAAQ,CAAC,YAAY,eAAc;AAC9C,cAAM,YAAY,mBAAAA,QAAG,QAAQ,wBACzB,QACA,mBAAAA,QAAG,QAAQ,mBACP,OAAO,QAAW,mBAAAA,QAAG,QAAQ,mBAAmB,UAAU,CAAC,GAC/D,mBAAAA,QAAG,QAAQ,oBAAoB,YAAY,eAAe,CAAC;AAE/D,iBAAS,aACL,kBAAkB,KAAK,kBAAkB,kBAAkB,WAAW,UAAU,CAAC;MACvF,CAAC;IACH,CAAC;EACH;EAGQ,qBAAqB,YAA2B,UAAgB;AACtE,QAAI,KAAK,uBAAuB,YAAY,QAAQ,GAAG;AACrD,WAAK,sBAAsB,YAAY,QAAQ;AAC/C,aAAO,mBAAAA,QAAG,QAAQ,iBAAiB,QAAQ;IAC7C;AAEA,QAAI,OAAO;AACX,QAAI,UAAU;AACd,OAAG;AACD,aAAO,GAAG,YAAY;IACxB,SAAS,CAAC,KAAK,uBAAuB,YAAY,IAAI;AAEtD,SAAK,sBAAsB,YAAY,IAAK;AAC5C,WAAO,mBAAAA,QAAG,QAAQ,iBAAiB,IAAK;EAC1C;EAMQ,uBAAuB,YAA2B,MAAY;AACpE,QAAI,KAAK,oBAAoB,IAAI,UAAU,KACvC,KAAK,oBAAoB,IAAI,UAAU,EAAG,QAAQ,IAAI,MAAM,IAAI;AAClE,aAAO;IACT;AAKA,UAAM,YAAuB,CAAC,UAAU;AACxC,WAAO,UAAU,QAAQ;AACvB,YAAM,OAAO,UAAU,MAAK;AAC5B,UAAI,mBAAAA,QAAG,aAAa,IAAI,KAAK,KAAK,SAAS,SAGtC,CAAC,mBAAAA,QAAG,kBAAkB,KAAK,MAAM,KAAK,KAAK,OAAO,iBAAiB,OAAO;AAC7E,eAAO;MACT;AACA,gBAAU,KAAK,GAAG,KAAK,YAAW,CAAE;IACtC;AACA,WAAO;EACT;EAEQ,sBAAsB,YAA2B,gBAAsB;AAC7E,SAAK,oBAAoB,IACrB,aAAa,KAAK,oBAAoB,IAAI,UAAU,KAAK,CAAA,GAAI,OAAO,cAAc,CAAC;EACzF;EAMQ,sBAAsB,MAAa;AACzC,UAAM,aAAa,KAAK,OAAM;AAC9B,UAAM,gBAAgB,mBAAAA,QAAG,yBAAyB,KAAK,cAAa,EAAG,MAAM,UAAU;AACvF,QAAI,CAAC,iBAAiB,CAAC,cAAc,QAAQ;AAC3C,aAAO;IACT;AACA,WAAO,cAAc,cAAc,SAAS,GAAI;EAClD;EAGQ,kBACJ,kBAA0B,WAC1B,YAAyB;AAC3B,UAAM,OAAO,KAAK,QAAQ,UAAU,mBAAAA,QAAG,SAAS,aAAa,WAAW,UAAU;AAMlF,WAAO,qBAAqB,IAAI,GAAG;IAAW;EAAK;EACrD;EAWQ,gBACJ,YAA2B,YAAoB,OAAoB,gBAAuB;AAC5F,UAAM,mBAAmB,mBAAAA,QAAG,QAAQ,iBAAiB,UAAU;AAC/D,UAAM,kBAAkB,QAAQ,mBAAAA,QAAG,QAAQ,iBAAiB,KAAK,IAAI;AACrE,UAAM,4BAA4B,KAAK,qBAAqB,YAAY,SAAS,UAAU;AAC3F,UAAM,2BAA2B,0BAA0B,UAAU,SAAS;AAC9E,QAAI;AACJ,QAAI;AAEJ,QAAI,4BAA4B,CAAC,gBAAgB;AAC/C,qBAAe;AACf,aAAO;IACT,WAAW,iBAAiB;AAC1B,qBAAe;AACf,aAAO;IACT,OAAO;AACL,aAAO;IACT;AAEA,WAAO,EAAC,cAAc,KAAI;EAC5B;EAGQ,eAAe,YAAyB;AAC9C,QAAI,CAAC,KAAK,YAAY,eAAe,WAAW,QAAQ,GAAG;AACzD,UAAI;AAGJ,iBAAW,aAAa,WAAW,YAAY;AAC7C,YAAI,mBAAAA,QAAG,oBAAoB,SAAS,KAChC,mBAAAA,QAAG,oBAAoB,UAAU,eAAe,GAAG;AAErD,uBAAa,UAAU,gBAAgB,QAAO,EAAG,KAAI,EAAG,WAAW,GAAG;AAGtE;QACF;MACF;AAGA,WAAK,YAAY,WAAW,YAAY,kCAAU;IACpD;AAEA,WAAO,KAAK,YAAY,WAAW;EACrC;;;;AD9UI,IAAO,gBAAP,MAAoB;EAIxB,YAAoB,UAA8B,iBAAgC;AAA9D;AAA8B;AAHjC,oCAAW,oBAAI,IAAG;AAClB;AAEG,SAAA,WAAA;AAA8B,SAAA,kBAAA;AAChD,SAAK,iBAAiB,IAAI,cACtB,kBAAgB;MACd,cAAc,CAAC,OAAO,SAAS,KAAK,WAAW,aAAa,OAAO,IAAI;MACvE,sBAAsB,CAAC,eAAe,SAAS,KAAK,YAChD,aAAa,cAAc,SAAQ,GAAI,cAAc,SAAQ,GAAI,IAAI;QAE3E,KAAK,QAAQ;EACnB;EAQA,WAAW,YAA2B,OAAe,MAAY;AAC/D,SAAK,aAAa,YAAY,EAAC,OAAO,OAAO,KAAI,CAAC;EACpD;EASA,YAAY,YAA2B,OAAe,cAAsB,MAAY;AACtF,SAAK,aAAa,YAAY,EAAC,OAAO,cAAc,KAAI,CAAC;EAC3D;EAWA,YACI,SAAkB,SAAkB,WAAW,mBAAAC,QAAG,SAAS,aAC3D,wBAAsC;AACxC,UAAM,aAAa,QAAQ,cAAa;AACxC,SAAK,YACD,YAAY,QAAQ,SAAQ,GAAI,QAAQ,SAAQ,GAChD,KAAK,SAAS,UAAU,UAAU,SAAS,0BAA0B,UAAU,CAAC;EACtF;EAMA,WAAW,MAAa;AACtB,SAAK,aACD,KAAK,cAAa,GAAI,EAAC,OAAO,KAAK,SAAQ,GAAI,cAAc,KAAK,SAAQ,GAAI,MAAM,GAAE,CAAC;EAC7F;EAQA,UACI,YAA2B,YAAoB,YAAoB,QAAqB,MACxF,iBAAiB,OAAK;AACxB,QAAI,KAAK,iBAAiB;AACxB,mBAAa,KAAK,gBAAgB,YAAY,WAAW,QAAQ;IACnE;AAKA,iBAAa,cAAc,UAAU;AAErC,WAAO,KAAK,eAAe,sBACvB,YAAY,YAAY,YAAY,OAAO,OAAO,cAAc;EACtE;EAMA,gBAAa;AACX,SAAK,eAAe,cAAa;AACjC,WAAO,KAAK;EACd;EAKA,eAAY;AACV,SAAK,SAAS,MAAK;EACrB;EAOQ,aAAa,MAAqB,QAAqB;AAC7D,UAAM,UAAU,KAAK,SAAS,IAAI,IAAI;AAEtC,QAAI,SAAS;AAIX,YAAM,cAAc,QAAQ,UAAU,aAAW,QAAQ,SAAS,OAAO,KAAK;AAE9E,UAAI,gBAAgB,IAAI;AACtB,gBAAQ,KAAK,MAAM;MACrB,OAAO;AACL,gBAAQ,OAAO,aAAa,GAAG,MAAM;MACvC;IACF,OAAO;AACL,WAAK,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IAClC;EACF;;AAII,SAAU,cAAcC,OAAY;AACxC,SAAOA,MAAK,QAAQ,OAAO,GAAG;AAChC;;;AExJA,IAAAC,qBAAe;AAuDT,SAAU,mBACZ,YAA2B,YAC3B,eAAqB;AAjEzB;AAkEE,UAAO,yBAAoB,YAAY,YAAY,CAAC,aAAa,CAAC,EAAE,OAA7D,YAAmE;AAC5E;AAEM,SAAU,oBACZ,YAA2B,YAC3B,gBAAwB;AAvE5B;AAwEE,QAAM,UAAgC,CAAA;AACtC,aAAW,QAAQ,WAAW,YAAY;AACxC,QAAI,mBAAAC,QAAG,oBAAoB,IAAI,KAAK,mBAAAA,QAAG,gBAAgB,KAAK,eAAe,GAAG;AAC5E,YAAM,UAAU,OAAO,eAAe,WAAW,KAAK,gBAAgB,SAAS,aAC9B,WAAW,KAAK,KAAK,gBAAgB,IAAI;AAC1F,YAAM,iBAAgB,UAAK,iBAAL,mBAAmB;AACzC,UAAI,WAAW,iBAAiB,mBAAAA,QAAG,eAAe,aAAa,GAAG;AAChE,mBAAW,iBAAiB,gBAAgB;AAC1C,gBAAM,QAAQ,oBAAoB,cAAc,UAAU,aAAa;AACvE,cAAI,OAAO;AACT,oBAAQ,KAAK,KAAK;UACpB;QACF;MACF;IACF;EACF;AACA,SAAO;AACT;AAiDM,SAAU,oBACZ,OAAyC,eAAqB;AAChE,SAAO,MAAM,KAAK,aAAU;AAC1B,UAAM,EAAC,MAAM,aAAY,IAAI;AAC7B,WAAO,eAAe,aAAa,SAAS,gBAAgB,KAAK,SAAS;EAC5E,CAAC;AACH;;;ACxIA,IAAAC,qBAAe;AAST,SAAU,YAA+B,MAAe,WAAiC;AAE7F,MAAI,UAAU,KAAK;AAEnB,SAAO,WAAW,CAAC,mBAAAC,QAAG,aAAa,OAAO,GAAG;AAC3C,QAAI,UAAU,OAAO,GAAG;AACtB,aAAO;IACT;AACA,cAAU,QAAQ;EACpB;AAEA,SAAO;AACT;;;AJfA,IAAM,aAAa;AACnB,IAAM,sBAAsB;AAC5B,IAAM,qBAAqB,oBAAI,IAAI,CAAC,UAAU,oBAAoB,CAAC;AAI7D,SAAU,YAAY,YAA2B,WAAoB;AApB3E;AAqBE,QAAM,wBAAwB,mBAAmB,YAAY,YAAY,mBAAmB;AAC5F,MAAI,0BAA0B,MAAM;AAClC;EACF;AAEA,QAAM,gBAAgB,IAAI,cAAc,mBAAAC,QAAG,cAAa,CAAE;AAE1D,oBAAkB,YAAY,aAAa;AAE3C,aAAW,iBAAiB,cAAc,cAAa,EAAG,OAAM,GAAI;AAClE,eAAW,UAAU,eAAe;AAClC,gBAAU,OAAO,QAAO,YAAO,iBAAP,YAAuB,GAAG,OAAO,IAAI;IAC/D;EACF;AACF;AAEA,SAAS,kBAAkB,YAA2B,eAA4B;AAChF,QAAM,mCACF,mBAAmB,YAAY,YAAY,4BAA4B;AAC3E,QAAM,eAAe,mCACjB,YAAY,kCAAkC,mBAAAA,QAAG,cAAc,IAC/D;AAEJ,QAAM,YAAY,CAAC,SAAiB;AA5CtC;AA6CI,QAAI,mBAAAA,QAAG,sBAAsB,IAAI,OAAK,UAAK,SAAL,mBAAW,eAAc,qBAAqB;AAClF;IACF;AAEA,QAAI,mBAAAA,QAAG,aAAa,IAAI,KAAK,mBAAmB,IAAI,KAAK,IAAI,GAAG;AAC9D,oBAAc,WAAW,KAAK,MAAM;AACpC,UAAI,KAAK,SAAS,sBAAsB;AACtC,YAAI,gBAAgB,kCAAkC;AACpD,wBAAc,WAAW,gCAAgC;QAC3D;MACF;IACF;AACA,uBAAAA,QAAG,aAAa,MAAM,SAAS;EACjC;AACA,qBAAAA,QAAG,aAAa,YAAY,SAAS;AACvC;;;AJ5Cc,SAAP,2BAAO;AACZ,SAAO,CAAO,SAAc;AAC1B,UAAM,EAAC,YAAY,UAAS,IAAI,MAAM,wBAAwB,IAAI;AAClE,UAAM,WAAW,QAAQ,IAAG;AAC5B,UAAM,WAAW,CAAC,GAAG,YAAY,GAAG,SAAS;AAE7C,QAAI,CAAC,SAAS,QAAQ;AACpB,YAAM,IAAI,sCACN,0FAA0F;IAChG;AAEA,eAAW,gBAAgB,UAAU;AACnC,4CAAsC,MAAM,cAAc,QAAQ;IACpE;EACF;AACF;AAEA,SAAS,sCAAsC,MAAY,cAAsB,UAAgB;AAC/F,QAAM,UAAU,uBAAuB,MAAM,cAAc,QAAQ;AACnE,QAAM,cAAc,QAAQ,eAAc,EAAG,OACzC,CAAC,eAAe,eAAe,UAAU,YAAY,OAAO,CAAC;AAEjE,aAAW,cAAc,aAAa;AACpC,QAAI,SAA8B;AAElC,UAAM,WAAW,CAAC,UAAkB,OAAe,SAAqB;AACtE,UAAI,WAAW,MAAM;AAEnB,iBAAS,KAAK,gBAAY,uBAAS,UAAU,WAAW,QAAQ,CAAC;MACnE;AACA,aAAO,OAAO,UAAU,KAAK;AAC7B,UAAI,SAAS,MAAM;AACjB,eAAO,WAAW,UAAU,IAAI;MAClC;IACF;AACA,gBAAY,YAAY,QAAQ;AAEhC,QAAI,WAAW,MAAM;AACnB,WAAK,aAAa,MAAM;IAC1B;EACF;AACF;", "names": ["import_path", "path", "import_typescript", "ts", "ts", "import_typescript", "import_typescript", "import_path", "import_typescript", "ts", "ts", "path", "import_typescript", "ts", "import_typescript", "ts", "ts"]}