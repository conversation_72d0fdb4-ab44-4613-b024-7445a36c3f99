/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { of } from 'rxjs';
import { map } from 'rxjs/operators';
import { runCanMatchGuards } from '../operators/check_guards';
import { defaultUrlMatcher, PRIMARY_OUTLET } from '../shared';
import { UrlSegmentGroup } from '../url_tree';
import { last } from './collection';
import { getOrCreateRouteInjectorIfNeeded, getOutlet } from './config';
const noMatch = {
    matched: false,
    consumedSegments: [],
    remainingSegments: [],
    parameters: {},
    positionalParamSegments: {},
};
export function matchWithChecks(segmentGroup, route, segments, injector, urlSerializer) {
    const result = match(segmentGroup, route, segments);
    if (!result.matched) {
        return of(result);
    }
    // Only create the Route's `EnvironmentInjector` if it matches the attempted
    // navigation
    injector = getOrCreateRouteInjectorIfNeeded(route, injector);
    return runCanMatchGuards(injector, route, segments, urlSerializer).pipe(map((v) => (v === true ? result : { ...noMatch })));
}
export function match(segmentGroup, route, segments) {
    if (route.path === '**') {
        return createWildcardMatchResult(segments);
    }
    if (route.path === '') {
        if (route.pathMatch === 'full' && (segmentGroup.hasChildren() || segments.length > 0)) {
            return { ...noMatch };
        }
        return {
            matched: true,
            consumedSegments: [],
            remainingSegments: segments,
            parameters: {},
            positionalParamSegments: {},
        };
    }
    const matcher = route.matcher || defaultUrlMatcher;
    const res = matcher(segments, segmentGroup, route);
    if (!res)
        return { ...noMatch };
    const posParams = {};
    Object.entries(res.posParams ?? {}).forEach(([k, v]) => {
        posParams[k] = v.path;
    });
    const parameters = res.consumed.length > 0
        ? { ...posParams, ...res.consumed[res.consumed.length - 1].parameters }
        : posParams;
    return {
        matched: true,
        consumedSegments: res.consumed,
        remainingSegments: segments.slice(res.consumed.length),
        // TODO(atscott): investigate combining parameters and positionalParamSegments
        parameters,
        positionalParamSegments: res.posParams ?? {},
    };
}
function createWildcardMatchResult(segments) {
    return {
        matched: true,
        parameters: segments.length > 0 ? last(segments).parameters : {},
        consumedSegments: segments,
        remainingSegments: [],
        positionalParamSegments: {},
    };
}
export function split(segmentGroup, consumedSegments, slicedSegments, config) {
    if (slicedSegments.length > 0 &&
        containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, config)) {
        const s = new UrlSegmentGroup(consumedSegments, createChildrenForEmptyPaths(config, new UrlSegmentGroup(slicedSegments, segmentGroup.children)));
        return { segmentGroup: s, slicedSegments: [] };
    }
    if (slicedSegments.length === 0 &&
        containsEmptyPathMatches(segmentGroup, slicedSegments, config)) {
        const s = new UrlSegmentGroup(segmentGroup.segments, addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, config, segmentGroup.children));
        return { segmentGroup: s, slicedSegments };
    }
    const s = new UrlSegmentGroup(segmentGroup.segments, segmentGroup.children);
    return { segmentGroup: s, slicedSegments };
}
function addEmptyPathsToChildrenIfNeeded(segmentGroup, slicedSegments, routes, children) {
    const res = {};
    for (const r of routes) {
        if (emptyPathMatch(segmentGroup, slicedSegments, r) && !children[getOutlet(r)]) {
            const s = new UrlSegmentGroup([], {});
            res[getOutlet(r)] = s;
        }
    }
    return { ...children, ...res };
}
function createChildrenForEmptyPaths(routes, primarySegment) {
    const res = {};
    res[PRIMARY_OUTLET] = primarySegment;
    for (const r of routes) {
        if (r.path === '' && getOutlet(r) !== PRIMARY_OUTLET) {
            const s = new UrlSegmentGroup([], {});
            res[getOutlet(r)] = s;
        }
    }
    return res;
}
function containsEmptyPathMatchesWithNamedOutlets(segmentGroup, slicedSegments, routes) {
    return routes.some((r) => emptyPathMatch(segmentGroup, slicedSegments, r) && getOutlet(r) !== PRIMARY_OUTLET);
}
function containsEmptyPathMatches(segmentGroup, slicedSegments, routes) {
    return routes.some((r) => emptyPathMatch(segmentGroup, slicedSegments, r));
}
function emptyPathMatch(segmentGroup, slicedSegments, r) {
    if ((segmentGroup.hasChildren() || slicedSegments.length > 0) && r.pathMatch === 'full') {
        return false;
    }
    return r.path === '';
}
/**
 * Determines if `route` is a path match for the `rawSegment`, `segments`, and `outlet` without
 * verifying that its children are a full match for the remainder of the `rawSegment` children as
 * well.
 */
export function isImmediateMatch(route, rawSegment, segments, outlet) {
    // We allow matches to empty paths when the outlets differ so we can match a url like `/(b:b)` to
    // a config like
    // * `{path: '', children: [{path: 'b', outlet: 'b'}]}`
    // or even
    // * `{path: '', outlet: 'a', children: [{path: 'b', outlet: 'b'}]`
    //
    // The exception here is when the segment outlet is for the primary outlet. This would
    // result in a match inside the named outlet because all children there are written as primary
    // outlets. So we need to prevent child named outlet matches in a url like `/b` in a config like
    // * `{path: '', outlet: 'x' children: [{path: 'b'}]}`
    // This should only match if the url is `/(x:b)`.
    if (getOutlet(route) !== outlet &&
        (outlet === PRIMARY_OUTLET || !emptyPathMatch(rawSegment, segments, route))) {
        return false;
    }
    return match(rawSegment, route, segments).matched;
}
export function noLeftoversInUrl(segmentGroup, segments, outlet) {
    return segments.length === 0 && !segmentGroup.children[outlet];
}
//# sourceMappingURL=data:application/json;base64,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